Imports System.Data.Entity
Imports System.Linq
Imports DxJBT2.Helpers

Public Class frmMenu
    Inherits BaseFormWithAccess

    Private context As dxjbt2Entities
    Private currentMenuId As Integer = 0

    Public Sub New()
        InitializeComponent()
        MenuCode = "MENU" ' Set menu code untuk access control
        context = New dxjbt2Entities()
    End Sub

    Private Sub frmMenu_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadParentMenus()
        LoadMenus()
        SetupGrid()
        ClearForm()
    End Sub

    Private Sub LoadParentMenus()
        Try
            ' Load parent menus untuk dropdown
            Dim parentMenus = context.tm_menu.Where(Function(m) m.IsActive AndAlso Not m.ParentMenu_id.HasValue).OrderBy(Function(m) m.NamaMenu).ToList()
            
            ' Add empty option
            Dim menuList = New List(Of Object)()
            menuList.Add(New With {.Id = 0, .NamaMenu = "-- Root Menu --"})
            menuList.AddRange(parentMenus.Select(Function(m) New With {.Id = m.Id, .NamaMenu = m.NamaMenu}))
            
            cboParentMenu.DataSource = menuList
            cboParentMenu.DisplayMember = "NamaMenu"
            cboParentMenu.ValueMember = "Id"
            cboParentMenu.SelectedIndex = 0

        Catch ex As Exception
            MessageBox.Show($"Error loading parent menus: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadMenus()
        Try
            ' Load semua menu dengan informasi parent
            Dim menus = From m In context.tm_menu
                       Where m.IsActive
                       Order By m.MenuOrder
                       Select New With {
                           .Id = m.Id,
                           .KodeMenu = m.KodeMenu,
                           .NamaMenu = m.NamaMenu,
                           .ParentMenu = If(m.ParentMenu_id.HasValue, 
                                          context.tm_menu.Where(Function(p) p.Id = m.ParentMenu_id.Value).Select(Function(p) p.NamaMenu).FirstOrDefault(), 
                                          "Root"),
                           .FormName = m.FormName,
                           .MenuIcon = m.MenuIcon,
                           .MenuOrder = m.MenuOrder,
                           .IsActive = m.IsActive
                       }

            dgvMenus.DataSource = menus.ToList()

        Catch ex As Exception
            MessageBox.Show($"Error loading menus: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetupGrid()
        Try
            dgvMenus.AutoGenerateColumns = False
            dgvMenus.Columns.Clear()

            ' Kolom ID (hidden)
            Dim colId = New DataGridViewTextBoxColumn()
            colId.Name = "Id"
            colId.DataPropertyName = "Id"
            colId.Visible = False
            dgvMenus.Columns.Add(colId)

            ' Kolom Kode Menu
            Dim colKode = New DataGridViewTextBoxColumn()
            colKode.Name = "KodeMenu"
            colKode.HeaderText = "Kode Menu"
            colKode.DataPropertyName = "KodeMenu"
            colKode.Width = 120
            colKode.ReadOnly = True
            dgvMenus.Columns.Add(colKode)

            ' Kolom Nama Menu
            Dim colNama = New DataGridViewTextBoxColumn()
            colNama.Name = "NamaMenu"
            colNama.HeaderText = "Nama Menu"
            colNama.DataPropertyName = "NamaMenu"
            colNama.Width = 200
            colNama.ReadOnly = True
            dgvMenus.Columns.Add(colNama)

            ' Kolom Parent Menu
            Dim colParent = New DataGridViewTextBoxColumn()
            colParent.Name = "ParentMenu"
            colParent.HeaderText = "Parent Menu"
            colParent.DataPropertyName = "ParentMenu"
            colParent.Width = 150
            colParent.ReadOnly = True
            dgvMenus.Columns.Add(colParent)

            ' Kolom Form Name
            Dim colForm = New DataGridViewTextBoxColumn()
            colForm.Name = "FormName"
            colForm.HeaderText = "Form Name"
            colForm.DataPropertyName = "FormName"
            colForm.Width = 150
            colForm.ReadOnly = True
            dgvMenus.Columns.Add(colForm)

            ' Kolom Menu Order
            Dim colOrder = New DataGridViewTextBoxColumn()
            colOrder.Name = "MenuOrder"
            colOrder.HeaderText = "Order"
            colOrder.DataPropertyName = "MenuOrder"
            colOrder.Width = 80
            colOrder.ReadOnly = True
            dgvMenus.Columns.Add(colOrder)

            ' Kolom Status
            Dim colStatus = New DataGridViewCheckBoxColumn()
            colStatus.Name = "IsActive"
            colStatus.HeaderText = "Active"
            colStatus.DataPropertyName = "IsActive"
            colStatus.Width = 80
            colStatus.ReadOnly = True
            dgvMenus.Columns.Add(colStatus)

        Catch ex As Exception
            MessageBox.Show($"Error setting up grid: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ClearForm()
        currentMenuId = 0
        txtKodeMenu.Clear()
        txtNamaMenu.Clear()
        txtFormName.Clear()
        txtMenuIcon.Clear()
        nudMenuOrder.Value = 1
        chkIsActive.Checked = True
        cboParentMenu.SelectedIndex = 0
        
        btnSave.Text = "Tambah"
        btnCancel.Enabled = False
    End Sub

    Private Sub LoadMenuToForm(menuId As Integer)
        Try
            Dim menu = context.tm_menu.Find(menuId)
            If menu IsNot Nothing Then
                currentMenuId = menu.Id
                txtKodeMenu.Text = menu.KodeMenu
                txtNamaMenu.Text = menu.NamaMenu
                txtFormName.Text = menu.FormName
                txtMenuIcon.Text = menu.MenuIcon
                nudMenuOrder.Value = menu.MenuOrder
                chkIsActive.Checked = menu.IsActive
                
                ' Set parent menu
                If menu.ParentMenu_id.HasValue Then
                    cboParentMenu.SelectedValue = menu.ParentMenu_id.Value
                Else
                    cboParentMenu.SelectedIndex = 0
                End If
                
                btnSave.Text = "Update"
                btnCancel.Enabled = True
            End If

        Catch ex As Exception
            MessageBox.Show($"Error loading menu: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Override methods dari BaseFormWithAccess
    Protected Overrides Sub OnAddClick()
        ClearForm()
    End Sub

    Protected Overrides Sub OnEditClick()
        If dgvMenus.CurrentRow IsNot Nothing Then
            Dim menuId = CInt(dgvMenus.CurrentRow.Cells("Id").Value)
            LoadMenuToForm(menuId)
        Else
            MessageBox.Show("Pilih menu yang akan diedit.", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Protected Overrides Sub OnDeleteClick()
        If dgvMenus.CurrentRow IsNot Nothing Then
            If MessageBox.Show("Apakah Anda yakin ingin menghapus menu ini?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                Try
                    Dim menuId = CInt(dgvMenus.CurrentRow.Cells("Id").Value)
                    Dim menu = context.tm_menu.Find(menuId)
                    If menu IsNot Nothing Then
                        menu.IsActive = False
                        menu.ModifiedDate = DateTime.Now
                        menu.ModifiedBy = SecurityHelper.GetCurrentUsername()
                        context.SaveChanges()
                        
                        MessageBox.Show("Menu berhasil dihapus.", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                        LoadMenus()
                        ClearForm()
                    End If
                Catch ex As Exception
                    MessageBox.Show($"Error deleting menu: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        Else
            MessageBox.Show("Pilih menu yang akan dihapus.", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If Not ValidateForm() Then Return

        Try
            Dim currentUser = SecurityHelper.GetCurrentUsername()
            
            If currentMenuId = 0 Then
                ' Add new menu
                If Not HasAccess(SecurityHelper.AccessRight.CanAdd) Then
                    ShowAccessDeniedMessage("menambah menu")
                    Return
                End If
                
                Dim newMenu = New tm_menu() With {
                    .KodeMenu = txtKodeMenu.Text.Trim(),
                    .NamaMenu = txtNamaMenu.Text.Trim(),
                    .ParentMenu_id = If(CInt(cboParentMenu.SelectedValue) = 0, Nothing, CInt(cboParentMenu.SelectedValue)),
                    .FormName = txtFormName.Text.Trim(),
                    .MenuIcon = txtMenuIcon.Text.Trim(),
                    .MenuOrder = CInt(nudMenuOrder.Value),
                    .IsActive = chkIsActive.Checked,
                    .CreatedDate = DateTime.Now,
                    .CreatedBy = currentUser
                }
                
                context.tm_menu.Add(newMenu)
                MessageBox.Show("Menu berhasil ditambahkan.", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                ' Update existing menu
                If Not HasAccess(SecurityHelper.AccessRight.CanEdit) Then
                    ShowAccessDeniedMessage("mengedit menu")
                    Return
                End If
                
                Dim menu = context.tm_menu.Find(currentMenuId)
                If menu IsNot Nothing Then
                    menu.KodeMenu = txtKodeMenu.Text.Trim()
                    menu.NamaMenu = txtNamaMenu.Text.Trim()
                    menu.ParentMenu_id = If(CInt(cboParentMenu.SelectedValue) = 0, Nothing, CInt(cboParentMenu.SelectedValue))
                    menu.FormName = txtFormName.Text.Trim()
                    menu.MenuIcon = txtMenuIcon.Text.Trim()
                    menu.MenuOrder = CInt(nudMenuOrder.Value)
                    menu.IsActive = chkIsActive.Checked
                    menu.ModifiedDate = DateTime.Now
                    menu.ModifiedBy = currentUser
                End If
                
                MessageBox.Show("Menu berhasil diupdate.", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            
            context.SaveChanges()
            LoadMenus()
            LoadParentMenus() ' Refresh parent menu dropdown
            ClearForm()

        Catch ex As Exception
            MessageBox.Show($"Error saving menu: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtKodeMenu.Text) Then
            MessageBox.Show("Kode Menu harus diisi.", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtKodeMenu.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtNamaMenu.Text) Then
            MessageBox.Show("Nama Menu harus diisi.", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNamaMenu.Focus()
            Return False
        End If

        ' Check duplicate kode menu
        Dim existingMenu = context.tm_menu.FirstOrDefault(Function(m) m.KodeMenu = txtKodeMenu.Text.Trim() AndAlso m.Id <> currentMenuId AndAlso m.IsActive)
        If existingMenu IsNot Nothing Then
            MessageBox.Show("Kode Menu sudah digunakan.", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtKodeMenu.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        ClearForm()
    End Sub

    Private Sub dgvMenus_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvMenus.CellDoubleClick
        If e.RowIndex >= 0 Then
            OnEditClick()
        End If
    End Sub

    Private Sub frmMenu_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        context?.Dispose()
    End Sub
End Class
