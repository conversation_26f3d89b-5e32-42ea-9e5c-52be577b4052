# Script untuk membuat ikon role dan user dengan pendekatan sederhana
Add-Type -AssemblyName System.Drawing

# Fungsi untuk membuat gambar dengan teks dan simbol
function New-SimpleIcon {
    param (
        [string]$outputPath,
        [System.Drawing.Color]$backgroundColor,
        [System.Drawing.Color]$foregroundColor,
        [string]$text,
        [string]$symbol
    )
    
    try {
        # Buat bitmap 32x32 pixels
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Aktifkan anti-aliasing untuk gambar yang lebih halus
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAliasGridFit
        
        # Isi dengan warna latar belakang
        $graphics.Clear($backgroundColor)
        
        # Siapkan font dan brush untuk teks
        $fontFamily = New-Object System.Drawing.FontFamily('Arial')
        $fontSize = 14
        $fontStyle = [System.Drawing.FontStyle]::Bold
        $font = New-Object System.Drawing.Font($fontFamily, $fontSize, $fontStyle)
        $brush = New-Object System.Drawing.SolidBrush($foregroundColor)
        
        # Hitung posisi teks agar berada di tengah
        $textSize = $graphics.MeasureString($text, $font)
        $x = (32 - $textSize.Width) / 2
        $y = (32 - $textSize.Height) / 2
        
        # Gambar simbol atau teks
        if ($symbol -eq "user") {
            # Gambar lingkaran untuk kepala
            $headSize = 10
            $headX = 16
            $headY = 10
            $headRect = New-Object System.Drawing.Rectangle(($headX - $headSize/2), ($headY - $headSize/2), $headSize, $headSize)
            $graphics.FillEllipse($brush, $headRect)
            
            # Gambar trapesium untuk badan
            $pen = New-Object System.Drawing.Pen($foregroundColor, 2)
            $bodyStartY = $headY + $headSize/2
            $graphics.DrawLine($pen, $headX, $bodyStartY, $headX, $bodyStartY + 12)
            $graphics.DrawLine($pen, $headX, $bodyStartY + 6, $headX - 6, $bodyStartY + 3)
            $graphics.DrawLine($pen, $headX, $bodyStartY + 6, $headX + 6, $bodyStartY + 3)
            $pen.Dispose()
        }
        elseif ($symbol -eq "role") {
            # Gambar beberapa lingkaran kecil untuk merepresentasikan grup
            $graphics.FillEllipse($brush, 10, 8, 6, 6)  # Kepala kiri
            $graphics.FillEllipse($brush, 16, 6, 8, 8)  # Kepala tengah (lebih besar)
            $graphics.FillEllipse($brush, 22, 8, 6, 6)  # Kepala kanan
            
            # Gambar garis bawah untuk menunjukkan "grup"
            $pen = New-Object System.Drawing.Pen($foregroundColor, 2)
            $graphics.DrawLine($pen, 8, 22, 24, 22)
            $pen.Dispose()
        }
        else {
            # Jika tidak ada simbol khusus, gambar teks
            $graphics.DrawString($text, $font, $brush, $x, $y)
        }
        
        # Simpan gambar
        $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Bersihkan resources
        $brush.Dispose()
        $font.Dispose()
        $graphics.Dispose()
        $bitmap.Dispose()
        
        Write-Host "Gambar berhasil dibuat: $outputPath"
    }
    catch {
        Write-Host "Error saat membuat gambar: $_"
    }
}

# Buat direktori Resources jika belum ada
$resourcesDir = "d:\SourceCode\Bright\DX24_1\DxJBT2\DxJBT2\Resources"
if (-not (Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir
}

# Buat ikon untuk menu Role
$rolePath = Join-Path $resourcesDir "Role_32x32.png"
New-SimpleIcon -outputPath $rolePath -backgroundColor ([System.Drawing.Color]::FromArgb(46, 204, 113)) -foregroundColor ([System.Drawing.Color]::White) -text "R" -symbol "role"

# Buat ikon untuk menu User
$userPath = Join-Path $resourcesDir "User_32x32.png"
New-SimpleIcon -outputPath $userPath -backgroundColor ([System.Drawing.Color]::FromArgb(142, 68, 173)) -foregroundColor ([System.Drawing.Color]::White) -text "U" -symbol "user"

Write-Host "Pembuatan ikon role dan user selesai."
