Imports DxJBT2
Imports System.IO.Ports

Public Class frmSettings
    Private Sub frmSettings_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Set form title
        Me.Text = "Application Settings"
        
        ' Load settings from database
        LoadSettings()
        
        ' Load available COM ports
        LoadComPorts()
    End Sub
    
    Private Sub LoadSettings()
        Try
            Dim dc As New dxjbt2Entities
            
            ' Check if COM port setting exists
            Dim comPortSetting = dc.tm_settings.FirstOrDefault(Function(s) s.SettingName = "WeighbridgeComPort")
            
            If comPortSetting IsNot Nothing Then
                cboComPort.Text = comPortSetting.SettingValue
            Else
                ' Default to COM1 if not configured
                cboComPort.Text = "COM1"
            End If
            
            ' Load other settings as needed
            ' Example: Baud rate, data bits, etc.
            Dim baudRateSetting = dc.tm_settings.FirstOrDefault(Function(s) s.SettingName = "WeighbridgeBaudRate")
            If baudRateSetting IsNot Nothing Then
                cboBaudRate.Text = baudRateSetting.SettingValue
            Else
                cboBaudRate.Text = "9600" ' Default
            End If
            
            ' Load dummy mode setting
            Dim dummyModeSetting = dc.tm_settings.FirstOrDefault(Function(s) s.SettingName = "WeighbridgeDummyMode")
            If dummyModeSetting IsNot Nothing Then
                chkDummyMode.Checked = (dummyModeSetting.SettingValue = "True")
            Else
                chkDummyMode.Checked = False ' Default to actual hardware
            End If
            
        Catch ex As Exception
            MessageBox.Show("Error loading settings: " & ex.Message, "Settings Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub LoadComPorts()
        Try
            ' Clear existing items
            cboComPort.Properties.Items.Clear()
            
            ' Get available COM ports
            Dim ports As String() = SerialPort.GetPortNames()
            
            ' Add to combo box
            For Each port As String In ports
                cboComPort.Properties.Items.Add(port)
            Next
            
            ' If no ports found, add some defaults
            If ports.Length = 0 Then
                For i As Integer = 1 To 10
                    cboComPort.Properties.Items.Add("COM" & i)
                Next
            End If
            
            ' Add baud rates
            cboBaudRate.Properties.Items.Clear()
            cboBaudRate.Properties.Items.AddRange(New Object() {"1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"})
            
        Catch ex As Exception
            MessageBox.Show("Error loading COM ports: " & ex.Message, "COM Port Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        Try
            Dim dc As New dxjbt2Entities
            
            ' Save COM port setting
            SaveSetting(dc, "WeighbridgeComPort", cboComPort.Text, "COM port for weighbridge connection")
            
            ' Save baud rate
            SaveSetting(dc, "WeighbridgeBaudRate", cboBaudRate.Text, "Baud rate for weighbridge connection")
            
            ' Save dummy mode setting
            SaveSetting(dc, "WeighbridgeDummyMode", chkDummyMode.Checked.ToString(), "Enable dummy mode for development without hardware")
            
            MessageBox.Show("Settings saved successfully.", "Settings Saved", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.Close()
            
        Catch ex As Exception
            MessageBox.Show("Error saving settings: " & ex.Message, "Settings Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub SaveSetting(dc As dxjbt2Entities, settingName As String, settingValue As String, description As String)
        ' Check if setting exists
        Dim setting = dc.tm_settings.FirstOrDefault(Function(s) s.SettingName = settingName)
        
        If setting IsNot Nothing Then
            ' Update existing setting
            setting.SettingValue = settingValue
            setting.LastUpdate = DateTime.Now
        Else
            ' Create new setting
            setting = New tm_settings() With {
                .SettingName = settingName,
                .SettingValue = settingValue,
                .Description = description,
                .LastUpdate = DateTime.Now
            }
            dc.tm_settings.Add(setting)
        End If
        
        ' Save changes
        dc.SaveChanges()
    End Sub
    
    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub
    
    Private Sub btnTest_Click(sender As Object, e As EventArgs) Handles btnTest.Click
        ' If dummy mode is checked, show success message without testing hardware
        If chkDummyMode.Checked Then
            MessageBox.Show("Dummy mode is enabled. No actual hardware test performed.", "Dummy Mode", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        
        ' Test COM port connection
        Dim serialPort As New SerialPort()
        
        Try
            With serialPort
                .PortName = cboComPort.Text
                .BaudRate = Integer.Parse(cboBaudRate.Text)
                .DataBits = 8
                .Parity = Parity.None
                .StopBits = StopBits.One
                .ReadTimeout = 500
                .WriteTimeout = 500
                .Open()
            End With
            
            MessageBox.Show("Connection successful! COM port is working.", "Test Successful", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
            ' Close port
            If serialPort.IsOpen Then
                serialPort.Close()
            End If
            
        Catch ex As Exception
            MessageBox.Show("Error connecting to COM port: " & ex.Message, "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            ' Ensure port is closed
            If serialPort IsNot Nothing AndAlso serialPort.IsOpen Then
                serialPort.Close()
            End If
        End Try
    End Sub
End Class
