<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmKemasan_edit
    Inherits frmEntryBase

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.DataLayoutControl1 = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.KodeKemasanTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TmkemasanBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.NamaKemasanTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.UkuranKemasanTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.BeratKemasanSpinEdit = New DevExpress.XtraEditors.SpinEdit()
        Me.SatuanTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.KeteranganMemoEdit = New DevExpress.XtraEditors.MemoEdit()
        Me.IsActiveCheckEdit = New DevExpress.XtraEditors.CheckEdit()
        Me.ItemForKodeKemasan = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForNamaKemasan = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForUkuranKemasan = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForBeratKemasan = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForSatuan = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForKeterangan = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForIsActive = New DevExpress.XtraLayout.LayoutControlItem()
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DataLayoutControl1.SuspendLayout()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.KodeKemasanTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TmkemasanBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NamaKemasanTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.UkuranKemasanTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BeratKemasanSpinEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SatuanTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.KeteranganMemoEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.IsActiveCheckEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForKodeKemasan, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNamaKemasan, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForUkuranKemasan, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForBeratKemasan, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForSatuan, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForKeterangan, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForIsActive, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblHeader
        '
        Me.lblHeader.Size = New System.Drawing.Size(579, 54)
        Me.lblHeader.Text = "       Master Kemasan"
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(443, 380)
        '
        'btnSave
        '
        Me.btnSave.Location = New System.Drawing.Point(359, 380)
        '
        'DataLayoutControl1
        '
        Me.DataLayoutControl1.Controls.Add(Me.KodeKemasanTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.NamaKemasanTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.UkuranKemasanTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.BeratKemasanSpinEdit)
        Me.DataLayoutControl1.Controls.Add(Me.SatuanTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.KeteranganMemoEdit)
        Me.DataLayoutControl1.Controls.Add(Me.IsActiveCheckEdit)
        Me.DataLayoutControl1.DataSource = Me.TmkemasanBindingSource
        Me.DataLayoutControl1.Location = New System.Drawing.Point(12, 68)
        Me.DataLayoutControl1.Name = "DataLayoutControl1"
        Me.DataLayoutControl1.Root = Me.LayoutControlGroup1
        Me.DataLayoutControl1.Size = New System.Drawing.Size(560, 300)
        Me.DataLayoutControl1.TabIndex = 35
        Me.DataLayoutControl1.Text = "DataLayoutControl1"
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForKodeKemasan, Me.ItemForNamaKemasan, Me.ItemForUkuranKemasan, Me.ItemForBeratKemasan, Me.ItemForSatuan, Me.ItemForKeterangan, Me.ItemForIsActive})
        Me.LayoutControlGroup1.Name = "LayoutControlGroup1"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(560, 300)
        Me.LayoutControlGroup1.TextVisible = False
        '
        'KodeKemasanTextEdit
        '
        Me.KodeKemasanTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkemasanBindingSource, "KodeKemasan", True))
        Me.KodeKemasanTextEdit.Location = New System.Drawing.Point(113, 12)
        Me.KodeKemasanTextEdit.Name = "KodeKemasanTextEdit"
        Me.KodeKemasanTextEdit.Properties.MaxLength = 20
        Me.KodeKemasanTextEdit.Size = New System.Drawing.Size(435, 22)
        Me.KodeKemasanTextEdit.StyleController = Me.DataLayoutControl1
        Me.KodeKemasanTextEdit.TabIndex = 4
        '
        'TmkemasanBindingSource
        '
        Me.TmkemasanBindingSource.DataSource = GetType(DxJBT2.tm_kemasan)
        '
        'NamaKemasanTextEdit
        '
        Me.NamaKemasanTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkemasanBindingSource, "NamaKemasan", True))
        Me.NamaKemasanTextEdit.Location = New System.Drawing.Point(113, 38)
        Me.NamaKemasanTextEdit.Name = "NamaKemasanTextEdit"
        Me.NamaKemasanTextEdit.Properties.MaxLength = 100
        Me.NamaKemasanTextEdit.Size = New System.Drawing.Size(435, 22)
        Me.NamaKemasanTextEdit.StyleController = Me.DataLayoutControl1
        Me.NamaKemasanTextEdit.TabIndex = 5
        '
        'UkuranKemasanTextEdit
        '
        Me.UkuranKemasanTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkemasanBindingSource, "UkuranKemasan", True))
        Me.UkuranKemasanTextEdit.Location = New System.Drawing.Point(113, 64)
        Me.UkuranKemasanTextEdit.Name = "UkuranKemasanTextEdit"
        Me.UkuranKemasanTextEdit.Properties.MaxLength = 50
        Me.UkuranKemasanTextEdit.Size = New System.Drawing.Size(435, 22)
        Me.UkuranKemasanTextEdit.StyleController = Me.DataLayoutControl1
        Me.UkuranKemasanTextEdit.TabIndex = 6
        '
        'BeratKemasanSpinEdit
        '
        Me.BeratKemasanSpinEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkemasanBindingSource, "BeratKemasan", True))
        Me.BeratKemasanSpinEdit.EditValue = New Decimal(New Integer() {0, 0, 0, 0})
        Me.BeratKemasanSpinEdit.Location = New System.Drawing.Point(113, 90)
        Me.BeratKemasanSpinEdit.Name = "BeratKemasanSpinEdit"
        Me.BeratKemasanSpinEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.BeratKemasanSpinEdit.Properties.Increment = New Decimal(New Integer() {1, 0, 0, 196608})
        Me.BeratKemasanSpinEdit.Properties.MaxValue = New Decimal(New Integer() {999999, 0, 0, 0})
        Me.BeratKemasanSpinEdit.Size = New System.Drawing.Size(435, 24)
        Me.BeratKemasanSpinEdit.StyleController = Me.DataLayoutControl1
        Me.BeratKemasanSpinEdit.TabIndex = 7
        '
        'SatuanTextEdit
        '
        Me.SatuanTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkemasanBindingSource, "Satuan", True))
        Me.SatuanTextEdit.Location = New System.Drawing.Point(113, 118)
        Me.SatuanTextEdit.Name = "SatuanTextEdit"
        Me.SatuanTextEdit.Properties.MaxLength = 20
        Me.SatuanTextEdit.Size = New System.Drawing.Size(435, 22)
        Me.SatuanTextEdit.StyleController = Me.DataLayoutControl1
        Me.SatuanTextEdit.TabIndex = 8
        '
        'KeteranganMemoEdit
        '
        Me.KeteranganMemoEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkemasanBindingSource, "Keterangan", True))
        Me.KeteranganMemoEdit.Location = New System.Drawing.Point(113, 144)
        Me.KeteranganMemoEdit.Name = "KeteranganMemoEdit"
        Me.KeteranganMemoEdit.Properties.MaxLength = 255
        Me.KeteranganMemoEdit.Size = New System.Drawing.Size(435, 116)
        Me.KeteranganMemoEdit.StyleController = Me.DataLayoutControl1
        Me.KeteranganMemoEdit.TabIndex = 9
        '
        'IsActiveCheckEdit
        '
        Me.IsActiveCheckEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkemasanBindingSource, "IsActive", True))
        Me.IsActiveCheckEdit.Location = New System.Drawing.Point(12, 264)
        Me.IsActiveCheckEdit.Name = "IsActiveCheckEdit"
        Me.IsActiveCheckEdit.Properties.Caption = "Aktif"
        Me.IsActiveCheckEdit.Size = New System.Drawing.Size(536, 24)
        Me.IsActiveCheckEdit.StyleController = Me.DataLayoutControl1
        Me.IsActiveCheckEdit.TabIndex = 10
        '
        'ItemForKodeKemasan
        '
        Me.ItemForKodeKemasan.Control = Me.KodeKemasanTextEdit
        Me.ItemForKodeKemasan.Location = New System.Drawing.Point(0, 0)
        Me.ItemForKodeKemasan.Name = "ItemForKodeKemasan"
        Me.ItemForKodeKemasan.Size = New System.Drawing.Size(540, 26)
        Me.ItemForKodeKemasan.Text = "Kode Kemasan"
        Me.ItemForKodeKemasan.TextSize = New System.Drawing.Size(89, 16)
        '
        'ItemForNamaKemasan
        '
        Me.ItemForNamaKemasan.Control = Me.NamaKemasanTextEdit
        Me.ItemForNamaKemasan.Location = New System.Drawing.Point(0, 26)
        Me.ItemForNamaKemasan.Name = "ItemForNamaKemasan"
        Me.ItemForNamaKemasan.Size = New System.Drawing.Size(540, 26)
        Me.ItemForNamaKemasan.Text = "Nama Kemasan"
        Me.ItemForNamaKemasan.TextSize = New System.Drawing.Size(89, 16)
        '
        'ItemForUkuranKemasan
        '
        Me.ItemForUkuranKemasan.Control = Me.UkuranKemasanTextEdit
        Me.ItemForUkuranKemasan.Location = New System.Drawing.Point(0, 52)
        Me.ItemForUkuranKemasan.Name = "ItemForUkuranKemasan"
        Me.ItemForUkuranKemasan.Size = New System.Drawing.Size(540, 26)
        Me.ItemForUkuranKemasan.Text = "Ukuran"
        Me.ItemForUkuranKemasan.TextSize = New System.Drawing.Size(89, 16)
        '
        'ItemForBeratKemasan
        '
        Me.ItemForBeratKemasan.Control = Me.BeratKemasanSpinEdit
        Me.ItemForBeratKemasan.Location = New System.Drawing.Point(0, 78)
        Me.ItemForBeratKemasan.Name = "ItemForBeratKemasan"
        Me.ItemForBeratKemasan.Size = New System.Drawing.Size(540, 28)
        Me.ItemForBeratKemasan.Text = "Berat (kg)"
        Me.ItemForBeratKemasan.TextSize = New System.Drawing.Size(89, 16)
        '
        'ItemForSatuan
        '
        Me.ItemForSatuan.Control = Me.SatuanTextEdit
        Me.ItemForSatuan.Location = New System.Drawing.Point(0, 106)
        Me.ItemForSatuan.Name = "ItemForSatuan"
        Me.ItemForSatuan.Size = New System.Drawing.Size(540, 26)
        Me.ItemForSatuan.Text = "Satuan"
        Me.ItemForSatuan.TextSize = New System.Drawing.Size(89, 16)
        '
        'ItemForKeterangan
        '
        Me.ItemForKeterangan.Control = Me.KeteranganMemoEdit
        Me.ItemForKeterangan.Location = New System.Drawing.Point(0, 132)
        Me.ItemForKeterangan.Name = "ItemForKeterangan"
        Me.ItemForKeterangan.Size = New System.Drawing.Size(540, 120)
        Me.ItemForKeterangan.Text = "Keterangan"
        Me.ItemForKeterangan.TextSize = New System.Drawing.Size(89, 16)
        '
        'ItemForIsActive
        '
        Me.ItemForIsActive.Control = Me.IsActiveCheckEdit
        Me.ItemForIsActive.Location = New System.Drawing.Point(0, 252)
        Me.ItemForIsActive.Name = "ItemForIsActive"
        Me.ItemForIsActive.Size = New System.Drawing.Size(540, 28)
        Me.ItemForIsActive.TextSize = New System.Drawing.Size(0, 0)
        Me.ItemForIsActive.TextVisible = False
        '
        'frmKemasan_edit
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(579, 424)
        Me.Controls.Add(Me.DataLayoutControl1)
        Me.Name = "frmKemasan_edit"
        Me.Text = "Master Kemasan"
        Me.Controls.SetChildIndex(Me.btnSave, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.lblHeader, 0)
        Me.Controls.SetChildIndex(Me.DataLayoutControl1, 0)
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DataLayoutControl1.ResumeLayout(False)
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.KodeKemasanTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TmkemasanBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NamaKemasanTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.UkuranKemasanTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BeratKemasanSpinEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SatuanTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.KeteranganMemoEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.IsActiveCheckEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForKodeKemasan, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNamaKemasan, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForUkuranKemasan, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForBeratKemasan, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForSatuan, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForKeterangan, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForIsActive, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents DataLayoutControl1 As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents TmkemasanBindingSource As BindingSource
    Friend WithEvents KodeKemasanTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents NamaKemasanTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents UkuranKemasanTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents BeratKemasanSpinEdit As DevExpress.XtraEditors.SpinEdit
    Friend WithEvents SatuanTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents KeteranganMemoEdit As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents IsActiveCheckEdit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForKodeKemasan As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForNamaKemasan As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForUkuranKemasan As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForBeratKemasan As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForSatuan As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForKeterangan As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForIsActive As DevExpress.XtraLayout.LayoutControlItem
End Class
