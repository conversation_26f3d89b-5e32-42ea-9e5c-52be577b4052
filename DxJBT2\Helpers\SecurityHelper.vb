Imports System.Data.Entity

Public Class SecurityHelper
    ' Enum untuk mendefinisikan hak akses
    Public Enum AccessRight
        ViewOnly = 1     ' Hanya melihat data
        CanAdd = 2       ' Dapat menambah data
        CanEdit = 4      ' Dapat mengedit data
        CanDelete = 8    ' Dapat menghapus data
        FullAccess = 15  ' Akses penuh (View + Add + Edit + Delete)
        NoAccess = 0     ' Tidak ada akses
    End Enum

    ' Enum untuk mendefinisikan modul aplikasi
    Public Enum AppModule
        UserManagement = 1
        RoleManagement = 2
        JembatanTimbang = 3
        MasterData = 4
        Reports = 5
    End Enum

    ' Fungsi untuk memeriksa apakah user memiliki hak akses tertentu pada modul tertentu
    Public Shared Function HasAccess(moduleId As AppModule, accessRight As AccessRight) As Boolean
        ' Jika belum login, tidak ada akses
        If My.Settings.CurrentUserId = 0 Then
            Return False
        End If

        ' Admin (Role_id = 1) selalu memiliki akses penuh
        If My.Settings.CurrentUserRole = 1 Then
            Return True
        End If

        ' Implementasi sederhana - dapat dikembangkan dengan tabel permission di database
        Select Case My.Settings.CurrentUserRole
            Case 1 ' Admin
                Return True
            Case 2 ' Operator
                ' Operator hanya memiliki akses ke modul Jembatan Timbang dan Reports
                If moduleId = AppModule.JembatanTimbang Then
                    Return (accessRight And (AccessRight.ViewOnly Or AccessRight.CanAdd Or AccessRight.CanEdit)) > 0
                ElseIf moduleId = AppModule.Reports Then
                    Return (accessRight And AccessRight.ViewOnly) > 0
                Else
                    Return False
                End If
            Case 3 ' Supervisor
                ' Supervisor memiliki akses ke semua modul kecuali User dan Role Management
                If moduleId = AppModule.UserManagement Or moduleId = AppModule.RoleManagement Then
                    Return False
                Else
                    Return True
                End If
            Case Else
                ' Role lain tidak memiliki akses khusus
                Return False
        End Select
    End Function

    ' Fungsi untuk mendapatkan nama user yang sedang login
    Public Shared Function GetCurrentUsername() As String
        Return My.Settings.CurrentUsername
    End Function

    ' Fungsi untuk mendapatkan ID user yang sedang login
    Public Shared Function GetCurrentUserId() As Integer
        Return My.Settings.CurrentUserId
    End Function

    ' Fungsi untuk mendapatkan role ID user yang sedang login
    Public Shared Function GetCurrentUserRole() As Integer
        Return My.Settings.CurrentUserRole
    End Function

    ' Fungsi untuk logout
    Public Shared Sub Logout()
        My.Settings.CurrentUserId = 0
        My.Settings.CurrentUsername = ""
        My.Settings.CurrentUserRole = 0
        My.Settings.Save()
    End Sub

    ' Fungsi untuk memeriksa apakah user sudah login
    Public Shared Function IsLoggedIn() As Boolean
        Return My.Settings.CurrentUserId > 0
    End Function
End Class
