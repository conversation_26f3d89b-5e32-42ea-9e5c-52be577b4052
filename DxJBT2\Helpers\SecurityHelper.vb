Imports System.Data.Entity

Public Class SecurityHelper
    ' Enum untuk mendefinisikan hak akses
    Public Enum AccessRight
        ViewOnly = 1     ' Hanya melihat data
        CanAdd = 2       ' Dapat menambah data
        CanEdit = 4      ' Dapat mengedit data
        CanDelete = 8    ' Dapat menghapus data
        CanPrint = 16    ' Dapat mencetak
        FullAccess = 31  ' Akses penuh (View + Add + Edit + Delete + Print)
        NoAccess = 0     ' Tidak ada akses
    End Enum

    ' Cache untuk menyimpan menu access user yang sedang login
    Private Shared _userMenuCache As List(Of vw_role_menu_access) = Nothing
    Private Shared _cacheUserId As Integer = 0

    ' Fungsi untuk memeriksa apakah user memiliki hak akses tertentu berdasarkan kode menu
    Public Shared Function HasMenuAccess(kodeMenu As String, accessRight As AccessRight) As Boolean
        ' Jika belum login, tidak ada akses
        If My.Settings.CurrentUserId = 0 Then
            Return False
        End If

        ' Load menu access jika belum ada atau user berbeda
        LoadUserMenuAccess()

        ' Cek apakah user adalah administrator
        If IsAdministrator() Then
            Return True
        End If

        ' Cari menu access berdasarkan kode menu
        Dim menuAccess = _userMenuCache?.FirstOrDefault(Function(m) m.KodeMenu = kodeMenu)
        If menuAccess Is Nothing Then
            Return False
        End If

        ' Cek hak akses berdasarkan AccessRight yang diminta
        Select Case accessRight
            Case AccessRight.ViewOnly
                Return menuAccess.CanView = 1
            Case AccessRight.CanAdd
                Return menuAccess.CanAdd = 1
            Case AccessRight.CanEdit
                Return menuAccess.CanEdit = 1
            Case AccessRight.CanDelete
                Return menuAccess.CanDelete = 1
            Case AccessRight.CanPrint
                Return menuAccess.CanPrint = 1
            Case Else
                Return False
        End Select
    End Function

    ' Fungsi untuk mendapatkan semua menu yang bisa diakses user
    Public Shared Function GetUserMenus() As List(Of vw_role_menu_access)
        ' Jika belum login, return empty list
        If My.Settings.CurrentUserId = 0 Then
            Return New List(Of vw_role_menu_access)()
        End If

        ' Load menu access jika belum ada atau user berbeda
        LoadUserMenuAccess()

        Return _userMenuCache
    End Function

    ' Fungsi untuk mendapatkan nama user yang sedang login
    Public Shared Function GetCurrentUsername() As String
        Return My.Settings.CurrentUsername
    End Function

    ' Fungsi untuk mendapatkan ID user yang sedang login
    Public Shared Function GetCurrentUserId() As Integer
        Return My.Settings.CurrentUserId
    End Function

    ' Fungsi untuk mendapatkan role ID user yang sedang login
    Public Shared Function GetCurrentUserRole() As Integer
        Return My.Settings.CurrentUserRole
    End Function

    ' Fungsi untuk logout
    Public Shared Sub Logout()
        My.Settings.CurrentUserId = 0
        My.Settings.CurrentUsername = ""
        My.Settings.CurrentUserRole = 0
        My.Settings.Save()

        ' Clear cache
        _userMenuCache = Nothing
        _cacheUserId = 0
    End Sub

    ' Fungsi untuk memeriksa apakah user sudah login
    Public Shared Function IsLoggedIn() As Boolean
        Return My.Settings.CurrentUserId > 0
    End Function

    ' Fungsi untuk memeriksa apakah user adalah administrator
    Public Shared Function IsAdministrator() As Boolean
        Try
            Using context As New dxjbt2Entities()
                Dim user = context.tm_user.Include("tm_role").FirstOrDefault(Function(u) u.Id = My.Settings.CurrentUserId)
                Return user IsNot Nothing AndAlso user.tm_role IsNot Nothing AndAlso user.tm_role.KodeRole = "ADMIN"
            End Using
        Catch ex As Exception
            ' Jika terjadi error, fallback ke role ID 1
            Return My.Settings.CurrentUserRole = 1
        End Try
    End Function

    ' Fungsi untuk load menu access user dari database
    Private Shared Sub LoadUserMenuAccess()
        ' Jika cache sudah ada dan user sama, tidak perlu reload
        If _userMenuCache IsNot Nothing AndAlso _cacheUserId = My.Settings.CurrentUserId Then
            Return
        End If

        Try
            Using context As New dxjbt2Entities()
                ' Jika user adalah administrator, ambil semua menu
                If IsAdministrator() Then
                    _userMenuCache = context.tm_menu.Where(Function(m) m.IsActive).
                        Select(Function(m) New vw_role_menu_access With {
                            .RoleId = My.Settings.CurrentUserRole,
                            .NamaRole = "Administrator",
                            .MenuId = m.Id,
                            .KodeMenu = m.KodeMenu,
                            .NamaMenu = m.NamaMenu,
                            .FormName = m.FormName,
                            .ParentMenuName = m.tm_menu2.NamaMenu,
                            .CanView = 1,
                            .CanAdd = 1,
                            .CanEdit = 1,
                            .CanDelete = 1,
                            .CanPrint = 1,
                            .MenuOrder = m.MenuOrder
                        }).ToList()
                Else
                    ' Untuk user non-admin, ambil dari view
                    _userMenuCache = context.vw_role_menu_access.
                        Where(Function(v) v.RoleId = My.Settings.CurrentUserRole AndAlso v.CanView = 1).
                        OrderBy(Function(v) v.MenuOrder).ToList()
                End If

                _cacheUserId = My.Settings.CurrentUserId
            End Using
        Catch ex As Exception
            ' Jika terjadi error, set cache kosong
            _userMenuCache = New List(Of vw_role_menu_access)()
            _cacheUserId = My.Settings.CurrentUserId
        End Try
    End Sub

    ' Fungsi untuk clear cache (dipanggil saat ada perubahan role/menu)
    Public Shared Sub ClearMenuCache()
        _userMenuCache = Nothing
        _cacheUserId = 0
    End Sub
End Class
