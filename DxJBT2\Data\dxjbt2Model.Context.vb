﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated from a template.
'
'     Manual changes to this file may cause unexpected behavior in your application.
'     Manual changes to this file will be overwritten if the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Imports System
Imports System.Data.Entity
Imports System.Data.Entity.Infrastructure
Imports System.Data.Entity.Core.Objects
Imports System.Linq

Partial Public Class dxjbt2Entities
    Inherits DbContext

    Public Sub New()
        MyBase.New("name=dxjbt2Entities")
    End Sub

    Protected Overrides Sub OnModelCreating(modelBuilder As DbModelBuilder)
        Throw New UnintentionalCodeFirstException()
    End Sub

    Public Overridable Property tm_kendaraan() As DbSet(Of tm_kendaraan)
    Public Overridable Property tm_mitra() As DbSet(Of tm_mitra)
    Public Overridable Property tm_tipe_kendaraan() As DbSet(Of tm_tipe_kendaraan)
    Public Overridable Property tr_jembatan_timbang() As DbSet(Of tr_jembatan_timbang)
    Public Overridable Property tm_settings() As DbSet(Of tm_settings)
    Public Overridable Property tm_role() As DbSet(Of tm_role)
    Public Overridable Property tm_user() As DbSet(Of tm_user)
    Public Overridable Property tm_kemasan() As DbSet(Of tm_kemasan)
    Public Overridable Property tr_kemasan_detail() As DbSet(Of tr_kemasan_detail)
    Public Overridable Property vw_jembatan_timbang_with_kemasan() As DbSet(Of vw_jembatan_timbang_with_kemasan)
    Public Overridable Property tm_menu() As DbSet(Of tm_menu)
    Public Overridable Property tm_role_menu_access() As DbSet(Of tm_role_menu_access)
    Public Overridable Property vw_role_menu_access() As DbSet(Of vw_role_menu_access)

    Public Overridable Function sp_GetUserMenuAccess(userId As Nullable(Of Integer)) As ObjectResult(Of sp_GetUserMenuAccess_Result)
        Dim userIdParameter As ObjectParameter = If(userId.HasValue, New ObjectParameter("UserId", userId), New ObjectParameter("UserId", GetType(Integer)))

        Return DirectCast(Me, IObjectContextAdapter).ObjectContext.ExecuteFunction(Of sp_GetUserMenuAccess_Result)("sp_GetUserMenuAccess", userIdParameter)
    End Function

End Class
