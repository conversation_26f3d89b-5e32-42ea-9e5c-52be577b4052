Imports System.Data.Entity
Imports DevExpress.XtraEditors
Imports DxJBT2.Helpers

Public Class frmLogin
    Inherits XtraForm


    Private _context As New dxjbt2Entities()
    Private _loginAttempts As Integer = 0

    Dim _mmaxLoginAttempts As Integer = 3

    Private Sub frmLogin_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        txtUsername.Focus()
    End Sub

    Private Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        If ValidateForm() Then
            Try
                ' Cek username
                Dim user = _context.tm_user.FirstOrDefault(Function(u) u.Username = txtUsername.Text.Trim())

                ' Jika user ditemukan, verifikasi password
                If user IsNot Nothing AndAlso PasswordHelper.VerifyPassword(txtPassword.Text.Trim(), user.Password) Then
                    ' Cek apakah user aktif
                    If Not user.IsActive Then
                        MessageBox.Show("Akun Anda tidak aktif. Silakan hubungi administrator.", "Login Gagal", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        Return
                    End If

                    ' Login berhasil
                    ' Simpan informasi user yang login
                    My.Settings.CurrentUserId = user.Id
                    My.Settings.CurrentUsername = user.Username
                    My.Settings.CurrentUserRole = user.Role_id
                    My.Settings.Save()

                    ' Tutup form login
                    Me.DialogResult = DialogResult.OK
                    Me.Close()
                Else
                    ' Login gagal
                    _loginAttempts += 1
                    If _loginAttempts >= _mmaxLoginAttempts Then
                        MessageBox.Show($"Anda telah mencoba login sebanyak {_loginAttempts} kali. Aplikasi akan ditutup.", "Login Gagal", MessageBoxButtons.OK, MessageBoxIcon.Error)
                        Application.Exit()
                    Else
                        MessageBox.Show($"Username atau password salah. Percobaan ke-{_loginAttempts} dari {_mmaxLoginAttempts}.", "Login Gagal", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        txtPassword.Clear()
                        txtPassword.Focus()
                    End If
                End If
            Catch ex As Exception
                MessageBox.Show($"Error saat login: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("Username harus diisi", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("Password harus diisi", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        ' Tutup aplikasi jika user membatalkan login
        Application.Exit()
    End Sub

    Private Sub txtPassword_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtPassword.KeyPress
        ' Jika user menekan Enter di textbox password, lakukan login
        If e.KeyChar = ChrW(Keys.Enter) Then
            e.Handled = True
            btnLogin_Click(sender, e)
        End If
    End Sub

    Private Sub txtUsername_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtUsername.KeyPress
        ' Jika user menekan Enter di textbox username, pindah ke password
        If e.KeyChar = ChrW(Keys.Enter) Then
            e.Handled = True
            txtPassword.Focus()
        End If
    End Sub
End Class
