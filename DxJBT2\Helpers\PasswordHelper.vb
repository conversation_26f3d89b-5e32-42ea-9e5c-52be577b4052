Imports System.Security.Cryptography
Imports System.Text

Public Class PasswordHelper
    ' Fungsi untuk mengenkripsi password dengan SHA256
    Public Shared Function EncryptPassword(ByVal password As String) As String
        If String.IsNullOrEmpty(password) Then
            Return String.Empty
        End If

        Using sha256 As SHA256 = SHA256.Create()
            ' Konversi password ke byte array
            Dim bytes As Byte() = Encoding.UTF8.GetBytes(password)
            
            ' Enkripsi password
            Dim hash As Byte() = sha256.ComputeHash(bytes)
            
            ' Konversi hasil enkripsi ke string hexadecimal
            Dim builder As New StringBuilder()
            For i As Integer = 0 To hash.Length - 1
                builder.Append(hash(i).ToString("x2"))
            Next

            Return builder.ToString()
        End Using
    End Function

    ' Fungsi untuk memverifikasi password
    Public Shared Function VerifyPassword(ByVal inputPassword As String, ByVal storedHash As String) As Boolean
        ' Jika password kosong atau hash kosong, return false
        If String.IsNullOrEmpty(inputPassword) OrElse String.IsNullOrEmpty(storedHash) Then
            Return False
        End If

        ' Enkripsi password input
        Dim inputHash As String = EncryptPassword(inputPassword)



        ' Bandingkan dengan hash yang tersimpan
        Return String.Equals(inputHash, storedHash, StringComparison.OrdinalIgnoreCase)
    End Function

    ' Fungsi untuk menghasilkan password default
    Public Shared Function GenerateDefaultPassword() As String
        Return "password123"
    End Function
End Class
