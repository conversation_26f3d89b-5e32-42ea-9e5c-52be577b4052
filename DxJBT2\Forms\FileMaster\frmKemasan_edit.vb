Imports System.Data.Entity
Imports DxJBT2.Helpers

Public Class frmKemasan_edit
    Inherits frmEntryBase

    Public Delegate Sub MsgHandler(ByVal msg As String)
    Public callBack As MsgHandler
    Public dbAction As String

    Public Sub New()
        ' This call is required by the designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
    End Sub

    Public Shared Sub showAs_Add(ByVal callBack As MsgHandler)
        Dim f As frmKemasan_edit = New frmKemasan_edit
        With f
            .callBack = callBack
            .dbAction = "add"
            .TmkemasanBindingSource.AddNew()
            .ShowDialog()
        End With
    End Sub

    Public Shared Sub showAs_Edit(ByVal callBack As MsgHandler, ByVal id As Integer)
        Dim f As frmKemasan_edit = New frmKemasan_edit
        With f
            .callBack = callBack
            .dbAction = "edit"

            Dim dc As New dxjbt2Entities
            .TmkemasanBindingSource.DataSource = (From c In dc.tm_kemasan Where c.Id = id Select c).ToList

            .ShowDialog()
        End With
    End Sub

    Public Shared Sub showAs_View(ByVal callBack As MsgHandler, ByVal id As Integer)
        Dim f As frmKemasan_edit = New frmKemasan_edit
        With f
            .callBack = callBack
            .dbAction = "view"

            Dim dc As New dxjbt2Entities
            .TmkemasanBindingSource.DataSource = (From c In dc.tm_kemasan Where c.Id = id Select c).ToList

            ' Disable editing in view mode
            .KodeKemasanTextEdit.ReadOnly = True
            .NamaKemasanTextEdit.ReadOnly = True
            .UkuranKemasanTextEdit.ReadOnly = True
            .BeratKemasanSpinEdit.ReadOnly = True
            .SatuanTextEdit.ReadOnly = True
            .KeteranganMemoEdit.ReadOnly = True
            .IsActiveCheckEdit.ReadOnly = True

            ' Change button visibility/text
            .btnSave.Visible = False
            .btnCancel.Text = "Close"

            .ShowDialog()
        End With
    End Sub

    Private Sub frmKemasan_edit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If dbAction = "add" Then
            ' Set default values for new record
            Dim o = CType(TmkemasanBindingSource.Current, tm_kemasan)
            If o IsNot Nothing Then
                o.IsActive = True
                o.CreatedDate = DateTime.Now
                o.Satuan = "pcs"
            End If
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If ValidateForm() Then
            If dbAction = "add" Then
                saveAdd()
            ElseIf dbAction = "edit" Then
                saveEdit()
            End If
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        ' Validate required fields
        If String.IsNullOrWhiteSpace(KodeKemasanTextEdit.Text) Then
            MessageBox.Show("Kode Kemasan harus diisi!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            KodeKemasanTextEdit.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(NamaKemasanTextEdit.Text) Then
            MessageBox.Show("Nama Kemasan harus diisi!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            NamaKemasanTextEdit.Focus()
            Return False
        End If

        If BeratKemasanSpinEdit.Value <= 0 Then
            MessageBox.Show("Berat Kemasan harus lebih dari 0!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            BeratKemasanSpinEdit.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub saveAdd()
        Try
            Dim o = CType(TmkemasanBindingSource.Current, tm_kemasan)
            Using dc As New dxjbt2Entities
                ' Check if code already exists
                Dim existingKemasan = dc.tm_kemasan.FirstOrDefault(Function(k) k.KodeKemasan = o.KodeKemasan AndAlso k.IsActive = True)
                If existingKemasan IsNot Nothing Then
                    MessageBox.Show("Kode Kemasan sudah ada!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

                o.CreatedDate = DateTime.Now
                o.CreatedBy = SecurityHelper.GetCurrentUsername()
                dc.tm_kemasan.Add(o)
                dc.SaveChanges()
            End Using
            callBack(o.Id.ToString)
            MessageBox.Show("Successfully Saved!", "Save", MessageBoxButtons.OK, MessageBoxIcon.Information)
            isClosing = True
            Close()
        Catch ex As Exception
            MessageBox.Show("Save Failed: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub saveEdit()
        Try
            Dim o = CType(TmkemasanBindingSource.Current, tm_kemasan)
            Using dc As New dxjbt2Entities
                Dim existingKemasan = dc.tm_kemasan.Find(o.Id)
                If existingKemasan IsNot Nothing Then
                    ' Check if code already exists (excluding current record)
                    Dim duplicateKemasan = dc.tm_kemasan.FirstOrDefault(Function(k) k.KodeKemasan = o.KodeKemasan AndAlso k.Id <> o.Id AndAlso k.IsActive = True)
                    If duplicateKemasan IsNot Nothing Then
                        MessageBox.Show("Kode Kemasan sudah ada!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        Return
                    End If

                    existingKemasan.KodeKemasan = o.KodeKemasan
                    existingKemasan.NamaKemasan = o.NamaKemasan
                    existingKemasan.UkuranKemasan = o.UkuranKemasan
                    existingKemasan.BeratKemasan = o.BeratKemasan
                    existingKemasan.Satuan = o.Satuan
                    existingKemasan.Keterangan = o.Keterangan
                    existingKemasan.IsActive = o.IsActive
                    existingKemasan.ModifiedDate = DateTime.Now
                    existingKemasan.ModifiedBy = SecurityHelper.GetCurrentUsername()
                    dc.SaveChanges()
                End If
            End Using
            callBack(o.Id.ToString)
            MessageBox.Show("Successfully Updated!", "Update", MessageBoxButtons.OK, MessageBoxIcon.Information)
            isClosing = True
            Close()
        Catch ex As Exception
            MessageBox.Show("Update Failed: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        isClosing = True
        Close()
    End Sub
End Class
