-- <PERSON><PERSON> Detail Kemasan per Transaksi
-- Menyimpan detail kemasan yang digunakan dalam setiap transaksi penimbangan

CREATE TABLE tr_kemasan_detail (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    JembatanTimbang_id INT NOT NULL,
    Kemasan_id INT NOT NULL,
    Qty INT NOT NULL DEFAULT 0, -- <PERSON><PERSON><PERSON>rat<PERSON>atuanKemasan DECIMAL(10,3) NOT NULL, -- Berat per unit kemasan (copy dari master)
    TotalBeratKemasan AS (Qty * BeratSatuanKemasan) PERSISTED, -- Calculated column
    Keterangan NVARCHAR(255) NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50) NULL,
    
    -- Foreign Key Constraints
    CONSTRAINT FK_tr_kemasan_detail_JembatanTimbang 
        FOREIGN KEY (JembatanTimbang_id) REFERENCES tr_jembatan_timbang(Id) 
        ON DELETE CASCADE,
    CONSTRAINT FK_tr_kemasan_detail_Kemasan 
        FOREIGN KEY (Kemasan_id) REFERENCES tm_kemasan(Id)
);

-- Index untuk performa
CREATE INDEX IX_tr_kemasan_detail_JembatanTimbang ON tr_kemasan_detail(JembatanTimbang_id);
CREATE INDEX IX_tr_kemasan_detail_Kemasan ON tr_kemasan_detail(Kemasan_id);

-- Constraint untuk memastikan Qty positif
ALTER TABLE tr_kemasan_detail 
ADD CONSTRAINT CK_tr_kemasan_detail_Qty_Positive 
CHECK (Qty > 0);

-- Constraint untuk memastikan BeratSatuanKemasan positif
ALTER TABLE tr_kemasan_detail 
ADD CONSTRAINT CK_tr_kemasan_detail_BeratSatuan_Positive 
CHECK (BeratSatuanKemasan >= 0);
