# Script untuk mengubah startup object di file project
$projectFile = "D:\SourceCode\Bright\DX24_1\DxJBT2\DxJBT2\DxJBT2.vbproj"

# Baca file project
$projectXml = [xml](Get-Content $projectFile)

# Cari PropertyGroup yang berisi StartupObject
$propertyGroups = $projectXml.Project.PropertyGroup
$startupObjectFound = $false

foreach ($group in $propertyGroups) {
    if ($group.StartupObject -ne $null) {
        # Ubah StartupObject menjadi DxJBT2.Startup
        $group.StartupObject = "DxJBT2.Startup"
        $startupObjectFound = $true
        Write-Host "StartupObject diubah menjadi DxJBT2.Startup"
        break
    }
}

# Jika StartupObject tidak ditemukan, tambahkan ke PropertyGroup pertama
if (-not $startupObjectFound) {
    $firstGroup = $propertyGroups[0]
    $startupObject = $projectXml.CreateElement("StartupObject")
    $startupObject.InnerText = "DxJBT2.Startup"
    $firstGroup.AppendChild($startupObject) | Out-Null
    Write-Host "StartupObject ditambahkan: DxJBT2.Startup"
}

# Ubah MySubMain menjadi true
foreach ($group in $propertyGroups) {
    if ($group.MyType -ne $null) {
        $group.MyType = "WindowsForms"
        Write-Host "MyType diubah menjadi WindowsForms"
    }
}

# Simpan perubahan
$projectXml.Save($projectFile)
Write-Host "File project berhasil diperbarui"
