﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmTipeKendaraan_Edit
    Inherits frmEntryBase

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim ConditionValidationRule1 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Me.DataLayoutControl1 = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.NamaTipeTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TmtipekendaraanBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForNamaTipe = New DevExpress.XtraLayout.LayoutControlItem()
        Me.DxValidationProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(Me.components)
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DataLayoutControl1.SuspendLayout()
        CType(Me.NamaTipeTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TmtipekendaraanBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNamaTipe, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblHeader
        '
        Me.lblHeader.Image = Global.DxJBT2.My.Resources.Resources.BOTask_32x32
        Me.lblHeader.Text = "       Data Entry TIPE KENDARAAN"
        '
        'btnSave
        '
        '
        'DataLayoutControl1
        '
        Me.DataLayoutControl1.Controls.Add(Me.NamaTipeTextEdit)
        Me.DataLayoutControl1.DataSource = Me.TmtipekendaraanBindingSource
        Me.DataLayoutControl1.Location = New System.Drawing.Point(7, 68)
        Me.DataLayoutControl1.Name = "DataLayoutControl1"
        Me.DataLayoutControl1.Root = Me.Root
        Me.DataLayoutControl1.Size = New System.Drawing.Size(422, 181)
        Me.DataLayoutControl1.TabIndex = 44
        Me.DataLayoutControl1.Text = "DataLayoutControl1"
        '
        'NamaTipeTextEdit
        '
        Me.NamaTipeTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmtipekendaraanBindingSource, "NamaTipe", True))
        Me.NamaTipeTextEdit.Location = New System.Drawing.Point(86, 12)
        Me.NamaTipeTextEdit.Name = "NamaTipeTextEdit"
        Me.NamaTipeTextEdit.Size = New System.Drawing.Size(324, 22)
        Me.NamaTipeTextEdit.StyleController = Me.DataLayoutControl1
        Me.NamaTipeTextEdit.TabIndex = 0
        ConditionValidationRule1.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule1.ErrorText = "Wajib Isi"
        Me.DxValidationProvider1.SetValidationRule(Me.NamaTipeTextEdit, ConditionValidationRule1)
        '
        'TmtipekendaraanBindingSource
        '
        Me.TmtipekendaraanBindingSource.DataSource = GetType(DxJBT2.tm_tipe_kendaraan)
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup1})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(422, 181)
        Me.Root.TextVisible = False
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.AllowDrawBackground = False
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForNamaTipe})
        Me.LayoutControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup1.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(402, 161)
        '
        'ItemForNamaTipe
        '
        Me.ItemForNamaTipe.Control = Me.NamaTipeTextEdit
        Me.ItemForNamaTipe.Location = New System.Drawing.Point(0, 0)
        Me.ItemForNamaTipe.Name = "ItemForNamaTipe"
        Me.ItemForNamaTipe.Size = New System.Drawing.Size(402, 161)
        Me.ItemForNamaTipe.Text = "Nama Tipe"
        Me.ItemForNamaTipe.TextSize = New System.Drawing.Size(62, 16)
        '
        'frmTipeKendaraan_Edit
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(709, 432)
        Me.Controls.Add(Me.DataLayoutControl1)
        Me.Name = "frmTipeKendaraan_Edit"
        Me.Text = "XtraForm1"
        Me.Controls.SetChildIndex(Me.lblHeader, 0)
        Me.Controls.SetChildIndex(Me.btnSave, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.DataLayoutControl1, 0)
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DataLayoutControl1.ResumeLayout(False)
        CType(Me.NamaTipeTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TmtipekendaraanBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNamaTipe, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents DataLayoutControl1 As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents TmtipekendaraanBindingSource As BindingSource
    Friend WithEvents NamaTipeTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForNamaTipe As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents DxValidationProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider
End Class
