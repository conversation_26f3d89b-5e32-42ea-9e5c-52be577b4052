﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmMitra_edit
    Inherits frmEntryBase

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim ConditionValidationRule1 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule2 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule3 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Me.DataLayoutControl1 = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.KodeMitraTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TmmitraBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.NamaMitraTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.AlamatMemoEdit = New DevExpress.XtraEditors.MemoEdit()
        Me.KotaTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TelpTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TipeMitraComboBoxEdit = New DevExpress.XtraEditors.ComboBoxEdit()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForKodeMitra = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForNamaMitra = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForAlamat = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForKota = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForTelp = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForTipeMitra = New DevExpress.XtraLayout.LayoutControlItem()
        Me.DxValidationProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(Me.components)
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DataLayoutControl1.SuspendLayout()
        CType(Me.KodeMitraTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TmmitraBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NamaMitraTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.AlamatMemoEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.KotaTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TelpTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TipeMitraComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForKodeMitra, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNamaMitra, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForAlamat, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForKota, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTelp, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTipeMitra, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblHeader
        '
        Me.lblHeader.Size = New System.Drawing.Size(1181, 54)
        Me.lblHeader.Text = "       Data Entry MITRA"
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(1067, 608)
        '
        'btnSave
        '
        Me.btnSave.Location = New System.Drawing.Point(985, 608)
        '
        'DataLayoutControl1
        '
        Me.DataLayoutControl1.Controls.Add(Me.KodeMitraTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.NamaMitraTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.AlamatMemoEdit)
        Me.DataLayoutControl1.Controls.Add(Me.KotaTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.TelpTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.TipeMitraComboBoxEdit)
        Me.DataLayoutControl1.DataSource = Me.TmmitraBindingSource
        Me.DataLayoutControl1.Location = New System.Drawing.Point(28, 95)
        Me.DataLayoutControl1.Name = "DataLayoutControl1"
        Me.DataLayoutControl1.Root = Me.Root
        Me.DataLayoutControl1.Size = New System.Drawing.Size(1088, 423)
        Me.DataLayoutControl1.TabIndex = 44
        Me.DataLayoutControl1.Text = "DataLayoutControl1"
        '
        'KodeMitraTextEdit
        '
        Me.KodeMitraTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmmitraBindingSource, "KodeMitra", True))
        Me.KodeMitraTextEdit.Location = New System.Drawing.Point(90, 12)
        Me.KodeMitraTextEdit.Name = "KodeMitraTextEdit"
        Me.KodeMitraTextEdit.Size = New System.Drawing.Size(986, 22)
        Me.KodeMitraTextEdit.StyleController = Me.DataLayoutControl1
        Me.KodeMitraTextEdit.TabIndex = 4
        ConditionValidationRule1.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule1.ErrorText = "Wajib Isi"
        Me.DxValidationProvider1.SetValidationRule(Me.KodeMitraTextEdit, ConditionValidationRule1)
        '
        'TmmitraBindingSource
        '
        Me.TmmitraBindingSource.DataSource = GetType(DxJBT2.tm_mitra)
        '
        'NamaMitraTextEdit
        '
        Me.NamaMitraTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmmitraBindingSource, "NamaMitra", True))
        Me.NamaMitraTextEdit.Location = New System.Drawing.Point(90, 38)
        Me.NamaMitraTextEdit.Name = "NamaMitraTextEdit"
        Me.NamaMitraTextEdit.Size = New System.Drawing.Size(986, 22)
        Me.NamaMitraTextEdit.StyleController = Me.DataLayoutControl1
        Me.NamaMitraTextEdit.TabIndex = 5
        ConditionValidationRule2.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule2.ErrorText = "Wajib isi"
        Me.DxValidationProvider1.SetValidationRule(Me.NamaMitraTextEdit, ConditionValidationRule2)
        '
        'AlamatMemoEdit
        '
        Me.AlamatMemoEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmmitraBindingSource, "Alamat", True))
        Me.AlamatMemoEdit.Location = New System.Drawing.Point(90, 90)
        Me.AlamatMemoEdit.Name = "AlamatMemoEdit"
        Me.AlamatMemoEdit.Size = New System.Drawing.Size(986, 269)
        Me.AlamatMemoEdit.StyleController = Me.DataLayoutControl1
        Me.AlamatMemoEdit.TabIndex = 6
        '
        'KotaTextEdit
        '
        Me.KotaTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmmitraBindingSource, "Kota", True))
        Me.KotaTextEdit.Location = New System.Drawing.Point(90, 363)
        Me.KotaTextEdit.Name = "KotaTextEdit"
        Me.KotaTextEdit.Size = New System.Drawing.Size(986, 22)
        Me.KotaTextEdit.StyleController = Me.DataLayoutControl1
        Me.KotaTextEdit.TabIndex = 7
        '
        'TelpTextEdit
        '
        Me.TelpTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmmitraBindingSource, "Telp", True))
        Me.TelpTextEdit.Location = New System.Drawing.Point(90, 389)
        Me.TelpTextEdit.Name = "TelpTextEdit"
        Me.TelpTextEdit.Size = New System.Drawing.Size(986, 22)
        Me.TelpTextEdit.StyleController = Me.DataLayoutControl1
        Me.TelpTextEdit.TabIndex = 8
        '
        'TipeMitraComboBoxEdit
        '
        Me.TipeMitraComboBoxEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmmitraBindingSource, "TipeMitra", True))
        Me.TipeMitraComboBoxEdit.Location = New System.Drawing.Point(90, 64)
        Me.TipeMitraComboBoxEdit.Name = "TipeMitraComboBoxEdit"
        Me.TipeMitraComboBoxEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TipeMitraComboBoxEdit.Properties.Items.AddRange(New Object() {"supplier", "customer"})
        Me.TipeMitraComboBoxEdit.Size = New System.Drawing.Size(986, 22)
        Me.TipeMitraComboBoxEdit.StyleController = Me.DataLayoutControl1
        Me.TipeMitraComboBoxEdit.TabIndex = 9
        ConditionValidationRule3.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule3.ErrorText = "Wajib isi"
        Me.DxValidationProvider1.SetValidationRule(Me.TipeMitraComboBoxEdit, ConditionValidationRule3)
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup1})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(1088, 423)
        Me.Root.TextVisible = False
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.AllowDrawBackground = False
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForKodeMitra, Me.ItemForNamaMitra, Me.ItemForAlamat, Me.ItemForKota, Me.ItemForTelp, Me.ItemForTipeMitra})
        Me.LayoutControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup1.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(1068, 403)
        '
        'ItemForKodeMitra
        '
        Me.ItemForKodeMitra.Control = Me.KodeMitraTextEdit
        Me.ItemForKodeMitra.Location = New System.Drawing.Point(0, 0)
        Me.ItemForKodeMitra.Name = "ItemForKodeMitra"
        Me.ItemForKodeMitra.Size = New System.Drawing.Size(1068, 26)
        Me.ItemForKodeMitra.Text = "Kode Mitra"
        Me.ItemForKodeMitra.TextSize = New System.Drawing.Size(66, 16)
        '
        'ItemForNamaMitra
        '
        Me.ItemForNamaMitra.Control = Me.NamaMitraTextEdit
        Me.ItemForNamaMitra.Location = New System.Drawing.Point(0, 26)
        Me.ItemForNamaMitra.Name = "ItemForNamaMitra"
        Me.ItemForNamaMitra.Size = New System.Drawing.Size(1068, 26)
        Me.ItemForNamaMitra.Text = "Nama Mitra"
        Me.ItemForNamaMitra.TextSize = New System.Drawing.Size(66, 16)
        '
        'ItemForAlamat
        '
        Me.ItemForAlamat.Control = Me.AlamatMemoEdit
        Me.ItemForAlamat.Location = New System.Drawing.Point(0, 78)
        Me.ItemForAlamat.Name = "ItemForAlamat"
        Me.ItemForAlamat.Size = New System.Drawing.Size(1068, 273)
        Me.ItemForAlamat.StartNewLine = True
        Me.ItemForAlamat.Text = "Alamat"
        Me.ItemForAlamat.TextSize = New System.Drawing.Size(66, 16)
        '
        'ItemForKota
        '
        Me.ItemForKota.Control = Me.KotaTextEdit
        Me.ItemForKota.Location = New System.Drawing.Point(0, 351)
        Me.ItemForKota.Name = "ItemForKota"
        Me.ItemForKota.Size = New System.Drawing.Size(1068, 26)
        Me.ItemForKota.Text = "Kota"
        Me.ItemForKota.TextSize = New System.Drawing.Size(66, 16)
        '
        'ItemForTelp
        '
        Me.ItemForTelp.Control = Me.TelpTextEdit
        Me.ItemForTelp.Location = New System.Drawing.Point(0, 377)
        Me.ItemForTelp.Name = "ItemForTelp"
        Me.ItemForTelp.Size = New System.Drawing.Size(1068, 26)
        Me.ItemForTelp.Text = "Telp"
        Me.ItemForTelp.TextSize = New System.Drawing.Size(66, 16)
        '
        'ItemForTipeMitra
        '
        Me.ItemForTipeMitra.Control = Me.TipeMitraComboBoxEdit
        Me.ItemForTipeMitra.Location = New System.Drawing.Point(0, 52)
        Me.ItemForTipeMitra.Name = "ItemForTipeMitra"
        Me.ItemForTipeMitra.Size = New System.Drawing.Size(1068, 26)
        Me.ItemForTipeMitra.Text = "Tipe Mitra"
        Me.ItemForTipeMitra.TextSize = New System.Drawing.Size(66, 16)
        '
        'frmMitra_edit
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1181, 671)
        Me.Controls.Add(Me.DataLayoutControl1)
        Me.Name = "frmMitra_edit"
        Me.Text = "frmMitra_edit"
        Me.Controls.SetChildIndex(Me.lblHeader, 0)
        Me.Controls.SetChildIndex(Me.btnSave, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.DataLayoutControl1, 0)
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DataLayoutControl1.ResumeLayout(False)
        CType(Me.KodeMitraTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TmmitraBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NamaMitraTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.AlamatMemoEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.KotaTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TelpTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TipeMitraComboBoxEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForKodeMitra, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNamaMitra, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForAlamat, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForKota, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTelp, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTipeMitra, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents DataLayoutControl1 As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents KodeMitraTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TmmitraBindingSource As BindingSource
    Friend WithEvents NamaMitraTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents AlamatMemoEdit As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents KotaTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TelpTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForKodeMitra As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForNamaMitra As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForAlamat As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForKota As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTelp As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents DxValidationProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider
    Friend WithEvents TipeMitraComboBoxEdit As DevExpress.XtraEditors.ComboBoxEdit
    Friend WithEvents ItemForTipeMitra As DevExpress.XtraLayout.LayoutControlItem
End Class
