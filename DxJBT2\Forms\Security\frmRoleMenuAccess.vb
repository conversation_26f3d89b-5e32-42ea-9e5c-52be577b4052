Imports System.Data.Entity
Imports System.Linq
Imports DxJBT2.Helpers

Public Class frmRoleMenuAccess
    Private context As dxjbt2Entities

    Private Sub frmRoleMenuAccess_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        context = New dxjbt2Entities()
        LoadRoles()
        LoadMenus()
        SetupGrid()
    End Sub

    Private Sub LoadRoles()
        Try
            Dim roles = context.tm_role.Where(Function(r) r.IsActive).OrderBy(Function(r) r.NamaRole).ToList()

            cboRole.DataSource = roles
            cboRole.DisplayMember = "NamaRole"
            cboRole.ValueMember = "Id"
            cboRole.SelectedIndex = -1

        Catch ex As Exception
            MessageBox.Show($"Error loading roles: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadMenus()
        Try
            ' Load semua menu aktif dengan struktur hierarki
            Dim menus = context.tm_menu.Where(Function(m) m.IsActive).OrderBy(Function(m) m.MenuOrder).ToList()

            ' Buat TreeView untuk menampilkan struktur menu
            tvMenus.Nodes.Clear()

            ' Tambahkan root menus
            Dim rootMenus = menus.Where(Function(m) Not m.ParentMenu_id.HasValue).ToList()
            For Each rootMenu In rootMenus
                Dim rootNode = tvMenus.Nodes.Add(rootMenu.KodeMenu, rootMenu.NamaMenu)
                rootNode.Tag = rootMenu

                ' Tambahkan child menus
                AddChildMenus(rootNode, menus, rootMenu.Id)
            Next

            tvMenus.ExpandAll()

        Catch ex As Exception
            MessageBox.Show($"Error loading menus: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub AddChildMenus(parentNode As TreeNode, allMenus As List(Of tm_menu), parentId As Integer)
        Dim childMenus = allMenus.Where(Function(m) m.ParentMenu_id.HasValue AndAlso m.ParentMenu_id.Value = parentId).ToList()

        For Each childMenu In childMenus
            Dim childNode = parentNode.Nodes.Add(childMenu.KodeMenu, childMenu.NamaMenu)
            childNode.Tag = childMenu

            ' Recursively add grandchildren
            AddChildMenus(childNode, allMenus, childMenu.Id)
        Next
    End Sub

    Private Sub SetupGrid()
        Try
            ' Setup grid untuk menampilkan menu access
            dgvMenuAccess.AutoGenerateColumns = False
            dgvMenuAccess.Columns.Clear()

            ' Kolom Menu
            Dim colMenu = New DataGridViewTextBoxColumn()
            colMenu.Name = "KodeMenu"
            colMenu.HeaderText = "Kode Menu"
            colMenu.DataPropertyName = "KodeMenu"
            colMenu.Width = 120
            colMenu.ReadOnly = True
            dgvMenuAccess.Columns.Add(colMenu)

            Dim colNamaMenu = New DataGridViewTextBoxColumn()
            colNamaMenu.Name = "NamaMenu"
            colNamaMenu.HeaderText = "Nama Menu"
            colNamaMenu.DataPropertyName = "NamaMenu"
            colNamaMenu.Width = 200
            colNamaMenu.ReadOnly = True
            dgvMenuAccess.Columns.Add(colNamaMenu)

            ' Kolom Access Rights (CheckBox)
            Dim colCanView = New DataGridViewCheckBoxColumn()
            colCanView.Name = "CanView"
            colCanView.HeaderText = "View"
            colCanView.DataPropertyName = "CanView"
            colCanView.Width = 60
            dgvMenuAccess.Columns.Add(colCanView)

            Dim colCanAdd = New DataGridViewCheckBoxColumn()
            colCanAdd.Name = "CanAdd"
            colCanAdd.HeaderText = "Add"
            colCanAdd.DataPropertyName = "CanAdd"
            colCanAdd.Width = 60
            dgvMenuAccess.Columns.Add(colCanAdd)

            Dim colCanEdit = New DataGridViewCheckBoxColumn()
            colCanEdit.Name = "CanEdit"
            colCanEdit.HeaderText = "Edit"
            colCanEdit.DataPropertyName = "CanEdit"
            colCanEdit.Width = 60
            dgvMenuAccess.Columns.Add(colCanEdit)

            Dim colCanDelete = New DataGridViewCheckBoxColumn()
            colCanDelete.Name = "CanDelete"
            colCanDelete.HeaderText = "Delete"
            colCanDelete.DataPropertyName = "CanDelete"
            colCanDelete.Width = 60
            dgvMenuAccess.Columns.Add(colCanDelete)

            Dim colCanPrint = New DataGridViewCheckBoxColumn()
            colCanPrint.Name = "CanPrint"
            colCanPrint.HeaderText = "Print"
            colCanPrint.DataPropertyName = "CanPrint"
            colCanPrint.Width = 60
            dgvMenuAccess.Columns.Add(colCanPrint)

            ' Hidden columns untuk ID
            Dim colMenuId = New DataGridViewTextBoxColumn()
            colMenuId.Name = "MenuId"
            colMenuId.DataPropertyName = "MenuId"
            colMenuId.Visible = False
            dgvMenuAccess.Columns.Add(colMenuId)

            Dim colAccessId = New DataGridViewTextBoxColumn()
            colAccessId.Name = "AccessId"
            colAccessId.DataPropertyName = "AccessId"
            colAccessId.Visible = False
            dgvMenuAccess.Columns.Add(colAccessId)

        Catch ex As Exception
            MessageBox.Show($"Error setting up grid: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub cboRole_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cboRole.SelectedIndexChanged
        If cboRole.SelectedValue IsNot Nothing Then
            LoadRoleMenuAccess(CInt(cboRole.SelectedValue))
        End If
    End Sub

    Private Sub LoadRoleMenuAccess(roleId As Integer)
        Try
            ' Ambil semua menu aktif
            Dim allMenus = context.tm_menu.Where(Function(m) m.IsActive).OrderBy(Function(m) m.MenuOrder).ToList()

            ' Ambil existing access untuk role ini
            Dim existingAccess = context.tm_role_menu_access.Where(Function(a) a.Role_id = roleId).ToList()

            ' Buat data untuk grid
            Dim gridData = New List(Of Object)()

            ' Convert to local variable to avoid VB.NET For Each property issue
            Dim menuList = allMenus.ToList()
            For Each Menu In menuList
                Dim access = existingAccess.FirstOrDefault(Function(a) a.Menu_id = Menu.Id)

                gridData.Add(New With {
                    .MenuId = Menu.Id,
                    .KodeMenu = Menu.KodeMenu,
                    .NamaMenu = Menu.NamaMenu,
                    .CanView = If(access IsNot Nothing, access.CanView, False),
                    .CanAdd = If(access IsNot Nothing, access.CanAdd, False),
                    .CanEdit = If(access IsNot Nothing, access.CanEdit, False),
                    .CanDelete = If(access IsNot Nothing, access.CanDelete, False),
                    .CanPrint = If(access IsNot Nothing, access.CanPrint, False),
                    .AccessId = If(access IsNot Nothing, access.Id, 0)
                })
            Next

            dgvMenuAccess.DataSource = gridData

        Catch ex As Exception
            MessageBox.Show($"Error loading role menu access: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If cboRole.SelectedValue Is Nothing Then
            MessageBox.Show("Pilih role terlebih dahulu.", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Dim roleId = CInt(cboRole.SelectedValue)
            Dim currentUser = SecurityHelper.GetCurrentUsername()

            ' Loop through grid data
            ' Convert to local variable to avoid VB.NET For Each property issue
            Dim rowCollection = dgvMenuAccess.Rows.Cast(Of DataGridViewRow)().ToList()
            For Each row As DataGridViewRow In rowCollection
                If row.IsNewRow Then Continue For

                Dim menuId = CInt(row.Cells("MenuId").Value)
                Dim accessId = CInt(row.Cells("AccessId").Value)
                Dim canView = CBool(row.Cells("CanView").Value)
                Dim canAdd = CBool(row.Cells("CanAdd").Value)
                Dim canEdit = CBool(row.Cells("CanEdit").Value)
                Dim canDelete = CBool(row.Cells("CanDelete").Value)
                Dim canPrint = CBool(row.Cells("CanPrint").Value)

                If accessId = 0 Then
                    ' Create new access record jika ada permission yang di-check
                    If canView OrElse canAdd OrElse canEdit OrElse canDelete OrElse canPrint Then
                        Dim newAccess = New tm_role_menu_access() With {
                            .Role_id = roleId,
                            .Menu_id = menuId,
                            .CanView = canView,
                            .CanAdd = canAdd,
                            .CanEdit = canEdit,
                            .CanDelete = canDelete,
                            .CanPrint = canPrint,
                            .CreatedDate = DateTime.Now,
                            .CreatedBy = currentUser
                        }
                        context.tm_role_menu_access.Add(newAccess)
                    End If
                Else
                    ' Update existing access record
                    Dim existingAccess = context.tm_role_menu_access.Find(accessId)
                    If existingAccess IsNot Nothing Then
                        ' Jika semua permission false, hapus record
                        If Not canView AndAlso Not canAdd AndAlso Not canEdit AndAlso Not canDelete AndAlso Not canPrint Then
                            context.tm_role_menu_access.Remove(existingAccess)
                        Else
                            ' Update record
                            existingAccess.CanView = canView
                            existingAccess.CanAdd = canAdd
                            existingAccess.CanEdit = canEdit
                            existingAccess.CanDelete = canDelete
                            existingAccess.CanPrint = canPrint
                            existingAccess.ModifiedDate = DateTime.Now
                            existingAccess.ModifiedBy = currentUser
                        End If
                    End If
                End If
            Next

            context.SaveChanges()

            ' Clear menu cache untuk refresh access rights
            SecurityHelper.ClearMenuCache()

            MessageBox.Show("Data berhasil disimpan.", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' Reload data
            LoadRoleMenuAccess(roleId)

        Catch ex As Exception
            MessageBox.Show($"Error saving data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnSelectAll_Click(sender As Object, e As EventArgs) Handles btnSelectAll.Click
        SetAllPermissions(True)
    End Sub

    Private Sub btnDeselectAll_Click(sender As Object, e As EventArgs) Handles btnDeselectAll.Click
        SetAllPermissions(False)
    End Sub

    Private Sub SetAllPermissions(value As Boolean)
        ' Convert to local variable to avoid VB.NET For Each property issue
        Dim rowCollection = dgvMenuAccess.Rows.Cast(Of DataGridViewRow)().ToList()
        For Each row As DataGridViewRow In rowCollection
            If row.IsNewRow Then Continue For

            row.Cells("CanView").Value = value
            row.Cells("CanAdd").Value = value
            row.Cells("CanEdit").Value = value
            row.Cells("CanDelete").Value = value
            row.Cells("CanPrint").Value = value
        Next
    End Sub

    Private Sub frmRoleMenuAccess_FormClosed(sender As Object, e As FormClosedEventArgs) Handles MyBase.FormClosed
        context?.Dispose()
    End Sub
End Class
