-- <PERSON><PERSON>t untuk insert data menu dan sample role access (<PERSON><PERSON><PERSON>)
-- Jalankan script ini jika tabel belum memiliki kolom CreatedBy/ModifiedBy

-- Hapus data existing jika ada (untuk testing)
DELETE FROM tm_role_menu_access;
DELETE FROM tm_menu;

-- Insert data menu utama (parent menus)
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive) VALUES
-- Main Menu Groups
('MASTER', 'Master Data', NULL, NULL, 'folder', 1, 1),
('TRANSAKSI', 'Transaksi', NULL, NULL, 'folder', 2, 1),
('LAPORAN', 'Laporan', NULL, NULL, 'folder', 3, 1),
('SECURITY', 'Security', NULL, NULL, 'folder', 4, 1),
('SETTING', 'Setting', NULL, NULL, 'folder', 5, 1);

-- Dapatkan ID dari parent menus untuk referensi
DECLARE @MasterID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'MASTER');
DECLARE @TransaksiID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'TRANSAKSI');
DECLARE @LaporanID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'LAPORAN');
DECLARE @SecurityID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'SECURITY');
DECLARE @SettingID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'SETTING');

-- Insert sub menus untuk Master Data (sesuai dengan yang ada di MainForm)
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive) VALUES
('KEMASAN', 'Kemasan', @MasterID, 'frmKemasan', 'box', 101, 1),
('KENDARAAN', 'Kendaraan', @MasterID, 'frmKendaraan', 'truck', 102, 1),
('MITRA', 'Mitra', @MasterID, 'frmMitra', 'users', 103, 1),
('TIPE_KENDARAAN', 'Tipe Kendaraan', @MasterID, 'frmTipeKendaraan', 'car', 104, 1);

-- Insert sub menus untuk Transaksi (sesuai dengan yang ada di MainForm)
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive) VALUES
('PENIMBANGAN', 'Penimbangan', @TransaksiID, 'frmJembatanTimbang', 'scale', 201, 1),
('KEMASAN_DETAIL', 'Detail Kemasan', @TransaksiID, 'frmKemasanDetail', 'package', 202, 1);

-- Insert sub menus untuk Laporan
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive) VALUES
('LAP_TRANSAKSI', 'Laporan Transaksi', @LaporanID, 'frmLaporanTransaksi', 'report', 301, 1),
('LAP_KEMASAN', 'Laporan Kemasan', @LaporanID, 'frmLaporanKemasan', 'chart', 302, 1),
('LAP_SUMMARY', 'Laporan Summary', @LaporanID, 'frmLaporanSummary', 'chart-bar', 303, 1);

-- Insert sub menus untuk Security (sesuai dengan yang ada di MainForm)
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive) VALUES
('USER', 'User', @SecurityID, 'frmUser', 'user', 401, 1),
('ROLE', 'Role', @SecurityID, 'frmRole', 'shield', 402, 1),
('ROLE_ACCESS', 'Role Menu Access', @SecurityID, 'frmRoleMenuAccess', 'key', 403, 1),
('MENU', 'Menu Management', @SecurityID, 'frmMenu', 'menu', 404, 1);

-- Insert sub menus untuk Setting
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive) VALUES
('SET_GENERAL', 'General Setting', @SettingID, 'frmSettings', 'settings', 501, 1),
('SET_PRINTER', 'Printer Setting', @SettingID, 'frmPrinterSettings', 'printer', 502, 1),
('SET_BACKUP', 'Backup & Restore', @SettingID, 'frmBackupRestore', 'database', 503, 1);

-- Sample data untuk role access (asumsi Role ID: 1 = Administrator, 2 = Operator, 3 = Viewer)
-- Administrator tidak perlu data karena akan di-handle di kode (full access)

-- Role Operator (Role_id = 2) - akses terbatas
-- Master Data (view only untuk kemasan dan kendaraan)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 2, Id, 1, 0, 0, 0, 1
FROM tm_menu WHERE KodeMenu IN ('KEMASAN', 'KENDARAAN', 'MITRA');

-- Transaksi (full access untuk jembatan timbang, limited untuk kemasan detail)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 2, Id, 1, 1, 1, 0, 1
FROM tm_menu WHERE KodeMenu = 'PENIMBANGAN';

INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 2, Id, 1, 1, 1, 1, 1
FROM tm_menu WHERE KodeMenu = 'KEMASAN_DETAIL';

-- Laporan (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 2, Id, 1, 0, 0, 0, 1
FROM tm_menu WHERE KodeMenu IN ('LAP_TRANSAKSI', 'LAP_KEMASAN');

-- Role Viewer (Role_id = 3) - hanya bisa melihat dan print
-- Master Data (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 3, Id, 1, 0, 0, 0, 1
FROM tm_menu WHERE KodeMenu IN ('KEMASAN', 'KENDARAAN', 'MITRA');

-- Transaksi (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 3, Id, 1, 0, 0, 0, 1
FROM tm_menu WHERE KodeMenu IN ('PENIMBANGAN', 'KEMASAN_DETAIL');

-- Laporan (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 3, Id, 1, 0, 0, 0, 1
FROM tm_menu WHERE KodeMenu IN ('LAP_TRANSAKSI', 'LAP_KEMASAN', 'LAP_SUMMARY');

-- Tambahkan akses khusus untuk Role Menu Access dan Menu Management (hanya untuk Administrator dan role tertentu)
-- Administrator akan bypass ini di kode, tapi kita tambahkan untuk konsistensi
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 1, Id, 1, 1, 1, 1, 1
FROM tm_menu WHERE KodeMenu IN ('ROLE_ACCESS', 'MENU');

-- Operator - akses terbatas ke Role Menu Access (hanya view dan edit)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 2, Id, 1, 0, 1, 0, 1
FROM tm_menu WHERE KodeMenu = 'ROLE_ACCESS';

-- Operator - akses terbatas ke Menu Management (view only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 2, Id, 1, 0, 0, 0, 1
FROM tm_menu WHERE KodeMenu = 'MENU';

-- Tambahkan akses untuk parent menu (agar bisa terlihat)
-- Operator - akses ke parent menu
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 2, Id, 1, 0, 0, 0, 0
FROM tm_menu WHERE KodeMenu IN ('MASTER', 'TRANSAKSI', 'LAPORAN', 'SECURITY');

-- Viewer - akses ke parent menu
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint)
SELECT 3, Id, 1, 0, 0, 0, 0
FROM tm_menu WHERE KodeMenu IN ('MASTER', 'TRANSAKSI', 'LAPORAN');

-- Verifikasi data yang sudah diinsert
SELECT 'Menu Data' as TableName, COUNT(*) as RecordCount FROM tm_menu WHERE IsActive = 1
UNION ALL
SELECT 'Role Menu Access Data' as TableName, COUNT(*) as RecordCount FROM tm_role_menu_access;

-- Tampilkan struktur menu
SELECT 
    CASE WHEN m.ParentMenu_id IS NULL THEN m.NamaMenu 
         ELSE '  └─ ' + m.NamaMenu END as MenuStructure,
    m.KodeMenu,
    m.FormName,
    m.MenuOrder
FROM tm_menu m
WHERE m.IsActive = 1
ORDER BY 
    ISNULL(m.ParentMenu_id, m.Id), 
    m.MenuOrder;

-- Tampilkan sample role access
SELECT 
    r.NamaRole,
    m.NamaMenu,
    rma.CanView,
    rma.CanAdd,
    rma.CanEdit,
    rma.CanDelete,
    rma.CanPrint
FROM tm_role_menu_access rma
INNER JOIN tm_role r ON rma.Role_id = r.Id
INNER JOIN tm_menu m ON rma.Menu_id = m.Id
WHERE r.IsActive = 1 AND m.IsActive = 1
ORDER BY r.NamaRole, m.MenuOrder;
