# Testing Role-Based Menu Access Control

## 📋 Overview
Dokumentasi ini menjelaskan cara testing sistem role-based menu access control yang telah diimplementasi.

## 🗄️ Database Setup

### 1. Jalankan Script Database
Jalankan script berikut secara berurutan:

```sql
-- 1. <PERSON><PERSON><PERSON> tabel sudah ada (dari migration sebelumnya)
-- tm_menu, tm_role, tm_user, tm_role_menu_access, vw_role_menu_access

-- 2. Insert data menu dan role access
-- File: DxJBT2/Database/InsertMenuData.sql

-- 3. Buat test users
-- File: DxJBT2/Database/CreateTestUsers.sql
```

### 2. Struktur Menu yang Dibuat

| Kode Menu | Nama Menu | Parent | Form Name |
|-----------|-----------|---------|-----------|
| MASTER | Master Data | - | - |
| KEMASAN | <PERSON><PERSON><PERSON> | MASTER | frmKemasan |
| KENDARAAN | Kendaraan | MASTER | frmKendaraan |
| MITRA | Mitra | MASTER | frmMitra |
| TIPE_KENDARAAN | Tipe Kendaraan | MASTER | frmTipeKendaraan |
| TRANSAKSI | Transaksi | - | - |
| PENIMBANGAN | Penimbangan | TRANSAKSI | frmJembatanTimbang |
| SECURITY | Security | - | - |
| USER | User | SECURITY | frmUser |
| ROLE | Role | SECURITY | frmRoleMenuAccess |

## 👥 Test Users

### Administrator (admin/admin123)
- **Role**: Administrator
- **Access**: Full access ke semua menu (bypass role access)
- **Behavior**: Semua menu visible dan accessible

### Operator (operator/operator123)
- **Role**: Operator
- **Access**: Limited access
- **Menu Access**:
  - ✅ KEMASAN (View, Print)
  - ✅ KENDARAAN (View, Print)
  - ✅ MITRA (View, Print)
  - ✅ PENIMBANGAN (View, Add, Edit, Print)
  - ❌ USER (No access)
  - ❌ ROLE (No access)

### Viewer (viewer/viewer123)
- **Role**: Viewer
- **Access**: Read-only access
- **Menu Access**:
  - ✅ KEMASAN (View, Print only)
  - ✅ KENDARAAN (View, Print only)
  - ✅ MITRA (View, Print only)
  - ✅ PENIMBANGAN (View, Print only)
  - ❌ USER (No access)
  - ❌ ROLE (No access)

### No Role User (norole/norole123)
- **Role**: None
- **Access**: No access to any menu
- **Behavior**: Semua menu hidden

## 🧪 Testing Scenarios

### 1. Login Testing
```vb
' Test di frmLogin atau SecurityHelper
Dim loginResult = SecurityHelper.Login("admin", "admin123")
' Expected: True, CurrentUser = admin, CurrentRole = Administrator

Dim loginResult = SecurityHelper.Login("operator", "operator123")
' Expected: True, CurrentUser = operator, CurrentRole = Operator

Dim loginResult = SecurityHelper.Login("viewer", "viewer123")
' Expected: True, CurrentUser = viewer, CurrentRole = Viewer

Dim loginResult = SecurityHelper.Login("norole", "norole123")
' Expected: True, CurrentUser = norole, CurrentRole = Nothing
```

### 2. Menu Visibility Testing
Login dengan user yang berbeda dan periksa:

**Administrator:**
- Semua menu di ToolStrip visible
- Semua dropdown menu items visible

**Operator:**
- ToolStripDropDownButton2 (Master Data) visible
- ToolStripButton1 (Transaksi) visible
- SecurityToolStripMenuItem hidden (karena tidak ada akses USER/ROLE)
- Individual menu items sesuai dengan access rights

**Viewer:**
- ToolStripDropDownButton2 (Master Data) visible
- ToolStripButton1 (Transaksi) visible
- SecurityToolStripMenuItem hidden
- Semua menu items visible tapi dengan access terbatas

**No Role User:**
- Semua menu hidden

### 3. Menu Access Testing
Klik menu items dan periksa:

**Administrator:**
- Semua menu dapat dibuka tanpa pesan error
- Form terbuka dengan full access rights

**Operator:**
- Menu KEMASAN: Dapat dibuka, tapi tombol Add/Edit disabled
- Menu PENIMBANGAN: Dapat dibuka dengan full access
- Menu USER/ROLE: Menampilkan pesan "Akses Ditolak"

**Viewer:**
- Semua menu yang visible dapat dibuka
- Semua tombol Add/Edit/Delete disabled
- Hanya tombol View dan Print yang enabled

### 4. Form Access Rights Testing
Untuk form yang inherit dari BaseFormWithAccess:

```vb
' Test di form yang inherit BaseFormWithAccess
Public Class frmKemasan
    Inherits BaseFormWithAccess
    
    Public Sub New()
        MyBase.New()
        MenuCode = "KEMASAN" ' Set kode menu
    End Sub
End Class
```

**Expected Behavior:**
- Administrator: Semua button enabled
- Operator: btnAdd, btnEdit, btnDelete disabled; btnView, btnPrint enabled
- Viewer: btnAdd, btnEdit, btnDelete disabled; btnView, btnPrint enabled

## 🔧 Debugging & Troubleshooting

### 1. Check Database Connection
```sql
-- Verify menu data
SELECT * FROM tm_menu WHERE IsActive = 1 ORDER BY MenuOrder;

-- Verify role access
SELECT r.NamaRole, m.KodeMenu, rma.* 
FROM tm_role_menu_access rma
INNER JOIN tm_role r ON rma.Role_id = r.Id
INNER JOIN tm_menu m ON rma.Menu_id = m.Id;

-- Verify user roles
SELECT u.Username, r.NamaRole 
FROM tm_user u 
LEFT JOIN tm_role r ON u.Role_id = r.Id;
```

### 2. Check SecurityHelper
```vb
' Debug current user info
Debug.WriteLine($"Current User: {SecurityHelper.GetCurrentUsername()}")
Debug.WriteLine($"Current Role: {SecurityHelper.GetCurrentUserRole()}")
Debug.WriteLine($"Is Admin: {SecurityHelper.IsAdministrator()}")

' Debug menu access
Debug.WriteLine($"KEMASAN Access: {SecurityHelper.HasMenuAccess("KEMASAN", SecurityHelper.AccessRight.ViewOnly)}")
```

### 3. Common Issues

**Menu tidak tersembunyi:**
- Periksa kode menu di database vs kode di MainForm
- Pastikan SetMenuAccess() dipanggil setelah login
- Periksa SecurityHelper.GetUserMenus() return data

**Access rights tidak bekerja:**
- Periksa BaseFormWithAccess.MenuCode sudah di-set
- Pastikan ApplyAccessRights() dipanggil
- Periksa nama button sesuai dengan yang dicari

**Database error:**
- Periksa connection string
- Pastikan semua tabel dan data sudah ada
- Periksa Entity Framework model

## 📊 Expected Results Summary

| User | Visible Menus | Accessible Menus | Button Access |
|------|---------------|------------------|---------------|
| admin | All | All | Full |
| operator | Master, Transaksi | KEMASAN(V,P), KENDARAAN(V,P), MITRA(V,P), PENIMBANGAN(Full) | Limited |
| viewer | Master, Transaksi | KEMASAN(V,P), KENDARAAN(V,P), MITRA(V,P), PENIMBANGAN(V,P) | View/Print only |
| norole | None | None | None |

## 🎯 Success Criteria

✅ **Menu Visibility**: Menu items hidden/shown based on role access
✅ **Access Control**: Unauthorized access blocked with appropriate messages  
✅ **Button States**: Form buttons enabled/disabled based on permissions
✅ **Role Management**: frmRoleMenuAccess works for managing permissions
✅ **Administrator Bypass**: Admin role has full access regardless of database settings
✅ **Error Handling**: Graceful handling of missing roles or database errors
