Imports System.Data.Entity

' DTO untuk kemasan detail untuk menghindari context issues
Public Class KemasanDetailDTO
    Public Property Id As Integer
    Public Property JembatanTimbang_id As Integer
    Public Property Kemasan_id As Integer
    Public Property Qty As Integer
    Public Property BeratSatuanKemasan As Decimal
    Public Property TotalBeratKemasan As Decimal
    Public Property Keterangan As String
    Public Property CreatedDate As DateTime
    Public Property CreatedBy As String

    ' Display properties
    Public Property KodeKemasan As String
    Public Property NamaKemasan As String
    Public Property UkuranKemasan As String

    Public Sub HitungTotalBerat()
        TotalBeratKemasan = Qty * BeratSatuanKemasan
    End Sub

    Public ReadOnly Property DisplayInfo As String
        Get
            If Not String.IsNullOrEmpty(KodeKemasan) Then
                Return $"{KodeKemasan} - {NamaKemasan} ({UkuranKemasan}) x {Qty} = {TotalBeratKemasan:N3} kg"
            Else
                Return $"Qty: {Qty} x {BeratSatuanKemasan:N3} kg = {TotalBeratKemasan:N3} kg"
            End If
        End Get
    End Property
End Class

Public Class frmKemasanDetail
    Inherits Form

    Public Property JembatanTimbangId As Integer
    Public Property KemasanDetailList As List(Of KemasanDetailDTO)
    Private _bindingSource As BindingSource
    Private _kemasanMasterList As List(Of tm_kemasan)

    Public Sub New()
        InitializeComponent()
        _bindingSource = New BindingSource()
        KemasanDetailList = New List(Of KemasanDetailDTO)()
        LoadKemasanMaster()
        SetupGrid()
    End Sub

    Private Sub LoadKemasanMaster()
        Try
            Using dc As New dxjbt2Entities
                ' Load kemasan master dengan cara yang lebih sederhana
                ' Disable lazy loading untuk menghindari context issues
                dc.Configuration.LazyLoadingEnabled = False

                ' Load semua data aktif terlebih dahulu
                _kemasanMasterList = dc.tm_kemasan.Where(Function(k) k.IsActive = True) _
                                                 .OrderBy(Function(k) k.KodeKemasan) _
                                                 .ToList()

                Console.WriteLine($"Loaded {_kemasanMasterList.Count} kemasan items")

                ' Setup repository untuk combo box di grid
                Dim kemasanRepo As New DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit()
                kemasanRepo.DataSource = _kemasanMasterList
                kemasanRepo.DisplayMember = "DisplayText"
                kemasanRepo.ValueMember = "Id"
                kemasanRepo.ShowHeader = False
                kemasanRepo.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup
                kemasanRepo.NullText = "-- Pilih Kemasan --"
                kemasanRepo.SearchMode = DevExpress.XtraEditors.Controls.SearchMode.AutoComplete

                ' Tambahkan event handler ke repository
                AddHandler kemasanRepo.EditValueChanged, AddressOf KemasanRepo_EditValueChanged

                ' Debug: cek apakah kolom ada
                Dim kemasanColumn = GridView1.Columns("Kemasan_id")
                If kemasanColumn IsNot Nothing Then
                    kemasanColumn.ColumnEdit = kemasanRepo
                    Console.WriteLine("Successfully assigned repository to Kemasan_id column")
                Else
                    Console.WriteLine("ERROR: Kemasan_id column not found!")
                    ' Debug: tampilkan semua kolom yang ada
                    For Each col As DevExpress.XtraGrid.Columns.GridColumn In GridView1.Columns
                        Console.WriteLine($"Available column: {col.FieldName}")
                    Next
                End If
            End Using
        Catch ex As Exception
            Console.WriteLine($"Error in LoadKemasanMaster: {ex.Message}")
            MessageBox.Show("Error loading kemasan master: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetupGrid()
        _bindingSource.DataSource = KemasanDetailList
        GridControl1.DataSource = _bindingSource

        ' Setup grid columns - read only mode
        With GridView1
            .OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.None
            .OptionsView.ShowGroupPanel = False
            .OptionsBehavior.Editable = False
            .OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False
            .OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False
            .OptionsBehavior.ReadOnly = True

            ' Set focus behavior
            .FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
            .OptionsSelection.EnableAppearanceFocusedRow = True
        End With
    End Sub

    Public Sub LoadData(jembatanTimbangId As Integer)
        Me.JembatanTimbangId = jembatanTimbangId

        If jembatanTimbangId > 0 Then
            Try
                Using dc As New dxjbt2Entities
                    ' Disable lazy loading
                    dc.Configuration.LazyLoadingEnabled = False

                    ' Load data dengan eager loading terlebih dahulu
                    Dim rawData = dc.tr_kemasan_detail.Include("tm_kemasan") _
                                    .Where(Function(kd) kd.JembatanTimbang_id = jembatanTimbangId) _
                                    .ToList()

                    ' Konversi ke DTO di memory
                    KemasanDetailList = rawData.Select(Function(kd) New KemasanDetailDTO With {
                        .Id = kd.Id,
                        .JembatanTimbang_id = kd.JembatanTimbang_id,
                        .Kemasan_id = kd.Kemasan_id,
                        .Qty = kd.Qty,
                        .BeratSatuanKemasan = kd.BeratSatuanKemasan,
                        .TotalBeratKemasan = If(kd.TotalBeratKemasan.HasValue, kd.TotalBeratKemasan.Value, kd.Qty * kd.BeratSatuanKemasan),
                        .Keterangan = kd.Keterangan,
                        .CreatedDate = kd.CreatedDate,
                        .CreatedBy = kd.CreatedBy,
                        .KodeKemasan = If(kd.tm_kemasan IsNot Nothing, kd.tm_kemasan.KodeKemasan, ""),
                        .NamaKemasan = If(kd.tm_kemasan IsNot Nothing, kd.tm_kemasan.NamaKemasan, ""),
                        .UkuranKemasan = If(kd.tm_kemasan IsNot Nothing, kd.tm_kemasan.UkuranKemasan, "")
                    }).ToList()
                End Using
            Catch ex As Exception
                MessageBox.Show("Error loading kemasan detail: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If

        _bindingSource.DataSource = KemasanDetailList
        _bindingSource.ResetBindings(False)
        HitungTotalBerat()
    End Sub

    Private Sub GridView1_CellValueChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs) Handles GridView1.CellValueChanged
        Try
            ' Debug: tampilkan field name yang sebenarnya
            Console.WriteLine($"CellValueChanged - FieldName: '{e.Column.FieldName}', Value: '{e.Value}'")

            If e.Column.FieldName = "Kemasan_id" Then
                ' Auto-fill berat satuan kemasan dari master
                Dim kemasanId As Object = GridView1.GetRowCellValue(e.RowHandle, "Kemasan_id")
                Console.WriteLine($"Kemasan ID selected: {kemasanId}")

                If kemasanId IsNot Nothing AndAlso IsNumeric(kemasanId) AndAlso CInt(kemasanId) > 0 Then
                    Dim kemasanIdInt As Integer = CInt(kemasanId)
                    Dim kemasan = _kemasanMasterList.FirstOrDefault(Function(k) k.Id = kemasanIdInt)

                    Console.WriteLine($"Found kemasan: {If(kemasan IsNot Nothing, kemasan.NamaKemasan, "Not Found")}")

                    If kemasan IsNot Nothing Then
                        ' Set nilai langsung ke grid
                        GridView1.SetRowCellValue(e.RowHandle, "BeratSatuanKemasan", kemasan.BeratKemasan)

                        ' Hitung total berat
                        Dim qtyValue As Object = GridView1.GetRowCellValue(e.RowHandle, "Qty")
                        Dim qty As Integer = If(IsNumeric(qtyValue), CInt(qtyValue), 1)
                        Dim totalBerat As Decimal = qty * kemasan.BeratKemasan
                        GridView1.SetRowCellValue(e.RowHandle, "TotalBeratKemasan", totalBerat)

                        Console.WriteLine($"Auto-filled: Berat={kemasan.BeratKemasan}, Qty={qty}, Total={totalBerat}")
                    End If
                End If

            ElseIf e.Column.FieldName = "Qty" OrElse e.Column.FieldName = "BeratSatuanKemasan" Then
                ' Recalculate total berat
                Dim qtyValue As Object = GridView1.GetRowCellValue(e.RowHandle, "Qty")
                Dim beratValue As Object = GridView1.GetRowCellValue(e.RowHandle, "BeratSatuanKemasan")

                If IsNumeric(qtyValue) AndAlso IsNumeric(beratValue) Then
                    Dim qty As Integer = CInt(qtyValue)
                    Dim beratSatuan As Decimal = CDec(beratValue)
                    Dim totalBerat As Decimal = qty * beratSatuan
                    GridView1.SetRowCellValue(e.RowHandle, "TotalBeratKemasan", totalBerat)
                End If
            End If

            ' Update total berat kemasan
            HitungTotalBerat()

        Catch ex As Exception
            ' Handle conversion errors gracefully
            Console.WriteLine($"Error in CellValueChanged: {ex.Message}")
            MessageBox.Show("Error updating kemasan data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End Try
    End Sub

    Private Sub GridView1_ValidatingEditor(sender As Object, e As DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventArgs) Handles GridView1.ValidatingEditor
        ' Pastikan nilai yang dipilih tersimpan dengan benar
        If GridView1.FocusedColumn.FieldName = "Kemasan_id" Then
            ' Force update nilai yang dipilih
            GridView1.PostEditor()
            GridView1.UpdateCurrentRow()
        End If
    End Sub

    Private Sub GridView1_FocusedRowChanged(sender As Object, e As DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs) Handles GridView1.FocusedRowChanged
        ' Pastikan data tersimpan saat pindah row
        If e.PrevFocusedRowHandle >= 0 Then
            GridView1.PostEditor()
            GridView1.UpdateCurrentRow()
        End If
    End Sub

    Private Sub GridView1_RowUpdated(sender As Object, e As DevExpress.XtraGrid.Views.Base.RowObjectEventArgs) Handles GridView1.RowUpdated
        ' Update total berat setelah row diupdate
        HitungTotalBerat()
    End Sub

    Private Sub GridView1_ShownEditor(sender As Object, e As EventArgs) Handles GridView1.ShownEditor
        ' Event ketika editor ditampilkan
        If GridView1.FocusedColumn.FieldName = "Kemasan_id" Then
            Console.WriteLine("Kemasan_id editor shown")

            ' Tambahkan event handler untuk lookup edit
            If TypeOf GridView1.ActiveEditor Is DevExpress.XtraEditors.LookUpEdit Then
                Dim lookupEdit = DirectCast(GridView1.ActiveEditor, DevExpress.XtraEditors.LookUpEdit)
                AddHandler lookupEdit.EditValueChanged, AddressOf LookupEdit_EditValueChanged
                Console.WriteLine("Added EditValueChanged handler to lookup edit")
            End If
        End If
    End Sub

    Private Sub LookupEdit_EditValueChanged(sender As Object, e As EventArgs)
        Try
            Dim lookupEdit = DirectCast(sender, DevExpress.XtraEditors.LookUpEdit)
            Dim selectedValue = lookupEdit.EditValue

            Console.WriteLine($"LookupEdit_EditValueChanged - Selected Value: {selectedValue}")

            If selectedValue IsNot Nothing AndAlso IsNumeric(selectedValue) Then
                Dim kemasanId As Integer = CInt(selectedValue)
                Dim kemasan = _kemasanMasterList.FirstOrDefault(Function(k) k.Id = kemasanId)

                If kemasan IsNot Nothing Then
                    Console.WriteLine($"Found kemasan in lookup change: {kemasan.NamaKemasan}")

                    ' Set nilai langsung ke grid
                    Dim currentRowHandle = GridView1.FocusedRowHandle
                    GridView1.SetRowCellValue(currentRowHandle, "BeratSatuanKemasan", kemasan.BeratKemasan)

                    ' Hitung total berat
                    Dim qtyValue As Object = GridView1.GetRowCellValue(currentRowHandle, "Qty")
                    Dim qty As Integer = If(IsNumeric(qtyValue), CInt(qtyValue), 1)
                    Dim totalBerat As Decimal = qty * kemasan.BeratKemasan
                    GridView1.SetRowCellValue(currentRowHandle, "TotalBeratKemasan", totalBerat)

                    Console.WriteLine($"Auto-filled from lookup: Berat={kemasan.BeratKemasan}, Qty={qty}, Total={totalBerat}")

                    ' Update total berat kemasan
                    HitungTotalBerat()
                End If
            End If

        Catch ex As Exception
            Console.WriteLine($"Error in LookupEdit_EditValueChanged: {ex.Message}")
        End Try
    End Sub

    Private Sub KemasanRepo_EditValueChanged(sender As Object, e As EventArgs)
        Try
            Dim lookupEdit = DirectCast(sender, DevExpress.XtraEditors.LookUpEdit)
            Dim selectedValue = lookupEdit.EditValue

            Console.WriteLine($"KemasanRepo_EditValueChanged - Selected Value: {selectedValue}")
            Console.WriteLine($"FocusedRowHandle: {GridView1.FocusedRowHandle}")
            Console.WriteLine($"RowCount: {GridView1.RowCount}")
            Console.WriteLine($"DataRowCount: {GridView1.DataRowCount}")

            If selectedValue IsNot Nothing AndAlso IsNumeric(selectedValue) Then
                Dim kemasanId As Integer = CInt(selectedValue)
                Dim kemasan = _kemasanMasterList.FirstOrDefault(Function(k) k.Id = kemasanId)

                If kemasan IsNot Nothing Then
                    Console.WriteLine($"Found kemasan in repo change: {kemasan.NamaKemasan}")

                    ' Coba berbagai cara untuk mendapatkan row handle yang valid
                    Dim currentRowHandle As Integer = GridView1.FocusedRowHandle

                    ' Jika FocusedRowHandle tidak valid, coba gunakan row yang sedang di-edit
                    If currentRowHandle < 0 Then
                        ' Cari row yang sedang dalam mode edit
                        For i As Integer = 0 To GridView1.DataRowCount - 1
                            If GridView1.IsRowSelected(i) Then
                                currentRowHandle = i
                                Exit For
                            End If
                        Next

                        ' Jika masih tidak ketemu, gunakan row terakhir (new row)
                        If currentRowHandle < 0 AndAlso GridView1.DataRowCount > 0 Then
                            currentRowHandle = GridView1.DataRowCount - 1
                        End If

                        ' Jika masih tidak ada, gunakan row 0
                        If currentRowHandle < 0 Then
                            currentRowHandle = 0
                        End If
                    End If

                    Console.WriteLine($"Using RowHandle: {currentRowHandle}")

                    If currentRowHandle >= 0 Then
                        ' Set kemasan_id terlebih dahulu
                        GridView1.SetRowCellValue(currentRowHandle, "Kemasan_id", kemasanId)
                        GridView1.SetRowCellValue(currentRowHandle, "BeratSatuanKemasan", kemasan.BeratKemasan)

                        ' Hitung total berat
                        Dim qtyValue As Object = GridView1.GetRowCellValue(currentRowHandle, "Qty")
                        Dim qty As Integer = If(IsNumeric(qtyValue), CInt(qtyValue), 1)
                        Dim totalBerat As Decimal = qty * kemasan.BeratKemasan
                        GridView1.SetRowCellValue(currentRowHandle, "TotalBeratKemasan", totalBerat)

                        Console.WriteLine($"Auto-filled from repo: Berat={kemasan.BeratKemasan}, Qty={qty}, Total={totalBerat}")

                        ' Force refresh grid
                        GridView1.RefreshData()
                        GridView1.UpdateCurrentRow()

                        ' Update total berat kemasan
                        HitungTotalBerat()
                    Else
                        Console.WriteLine("ERROR: Could not determine valid row handle")
                    End If
                End If
            End If

        Catch ex As Exception
            Console.WriteLine($"Error in KemasanRepo_EditValueChanged: {ex.Message}")
        End Try
    End Sub

    Private Sub GridView1_InitNewRow(sender As Object, e As DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs) Handles GridView1.InitNewRow
        ' Jangan tambahkan ke list di sini, biarkan grid handle new row
        ' Set default values untuk new row
        GridView1.SetRowCellValue(e.RowHandle, "JembatanTimbang_id", JembatanTimbangId)
        GridView1.SetRowCellValue(e.RowHandle, "Qty", 1)
        GridView1.SetRowCellValue(e.RowHandle, "BeratSatuanKemasan", 0)
        GridView1.SetRowCellValue(e.RowHandle, "TotalBeratKemasan", 0)
        GridView1.SetRowCellValue(e.RowHandle, "CreatedDate", DateTime.Now)
        GridView1.SetRowCellValue(e.RowHandle, "CreatedBy", "System")
    End Sub

    Private Sub GridView1_ValidateRow(sender As Object, e As DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs) Handles GridView1.ValidateRow
        Dim row = TryCast(GridView1.GetRow(e.RowHandle), KemasanDetailDTO)
        If row IsNot Nothing Then
            If row.Kemasan_id <= 0 Then
                e.Valid = False
                e.ErrorText = "Pilih jenis kemasan!"
                Return
            End If

            If row.Qty <= 0 Then
                e.Valid = False
                e.ErrorText = "Qty harus lebih dari 0!"
                Return
            End If

            If row.BeratSatuanKemasan < 0 Then
                e.Valid = False
                e.ErrorText = "Berat satuan tidak boleh negatif!"
                Return
            End If
        End If
    End Sub

    Private Sub HitungTotalBerat()
        Dim total As Decimal = KemasanDetailList.Sum(Function(k) k.TotalBeratKemasan)
        lblTotalBerat.Text = $"Total Berat Kemasan: {total:N3} kg"
    End Sub

    Public Function GetTotalBeratKemasan() As Decimal
        Return KemasanDetailList.Sum(Function(k) k.TotalBeratKemasan)
    End Function

    Public Function SaveData() As Boolean
        Try
            Using dc As New dxjbt2Entities
                ' Delete existing records
                Dim existingRecords = dc.tr_kemasan_detail.Where(Function(k) k.JembatanTimbang_id = JembatanTimbangId).ToList()
                dc.tr_kemasan_detail.RemoveRange(existingRecords)

                ' Add new records - convert DTO to entity
                For Each dtoItem In KemasanDetailList.Where(Function(k) k.Kemasan_id > 0 AndAlso k.Qty > 0)
                    Dim entity As New tr_kemasan_detail()
                    entity.JembatanTimbang_id = JembatanTimbangId
                    entity.Kemasan_id = dtoItem.Kemasan_id
                    entity.Qty = dtoItem.Qty
                    entity.BeratSatuanKemasan = dtoItem.BeratSatuanKemasan
                    entity.Keterangan = dtoItem.Keterangan
                    entity.CreatedDate = DateTime.Now
                    entity.CreatedBy = "System" ' TODO: Get current user

                    ' Calculate total berat
                    entity.HitungTotalBerat()

                    dc.tr_kemasan_detail.Add(entity)
                Next

                dc.SaveChanges()
                Return True
            End Using
        Catch ex As Exception
            MessageBox.Show("Error saving kemasan detail: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        If SaveData() Then
            DialogResult = DialogResult.OK
            Close()
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        DialogResult = DialogResult.Cancel
        Close()
    End Sub

    Private Sub btnAddRow_Click(sender As Object, e As EventArgs) Handles btnAddRow.Click
        Try
            Dim frmEntry As New frmKemasanEntry()
            If frmEntry.ShowDialog() = DialogResult.OK Then
                ' Set JembatanTimbang_id
                frmEntry.KemasanDetailData.JembatanTimbang_id = JembatanTimbangId

                ' Add to list
                KemasanDetailList.Add(frmEntry.KemasanDetailData)

                ' Refresh grid
                _bindingSource.ResetBindings(False)
                HitungTotalBerat()
            End If
        Catch ex As Exception
            MessageBox.Show("Error adding kemasan: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnEditRow_Click(sender As Object, e As EventArgs) Handles btnEditRow.Click
        If GridView1.FocusedRowHandle >= 0 Then
            Try
                Dim row = TryCast(GridView1.GetRow(GridView1.FocusedRowHandle), KemasanDetailDTO)
                If row IsNot Nothing Then
                    Dim frmEntry As New frmKemasanEntry(row)
                    If frmEntry.ShowDialog() = DialogResult.OK Then
                        ' Update data in list
                        Dim index = KemasanDetailList.IndexOf(row)
                        If index >= 0 Then
                            KemasanDetailList(index) = frmEntry.KemasanDetailData
                            KemasanDetailList(index).JembatanTimbang_id = JembatanTimbangId

                            ' Refresh grid
                            _bindingSource.ResetBindings(False)
                            HitungTotalBerat()
                        End If
                    End If
                End If
            Catch ex As Exception
                MessageBox.Show("Error editing kemasan: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnDeleteRow_Click(sender As Object, e As EventArgs) Handles btnDeleteRow.Click
        If GridView1.FocusedRowHandle >= 0 Then
            Try
                Dim row = TryCast(GridView1.GetRow(GridView1.FocusedRowHandle), KemasanDetailDTO)
                If row IsNot Nothing Then
                    Dim result = MessageBox.Show($"Hapus kemasan {row.NamaKemasan}?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                    If result = DialogResult.Yes Then
                        KemasanDetailList.Remove(row)
                        _bindingSource.ResetBindings(False)
                        HitungTotalBerat()
                    End If
                End If
            Catch ex As Exception
                MessageBox.Show("Error deleting kemasan: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub frmKemasanDetail_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Detail Kemasan - Transaksi"
        HitungTotalBerat()
    End Sub
End Class
