Imports System.Data.Entity
Imports DxJBT2.Helpers

Public Class frmKemasanEntry
    Inherits Form

    Public Property KemasanDetailData As KemasanDetailDTO
    Private _kemasanMasterList As List(Of tm_kemasan)
    Private _isEditMode As Boolean = False

    Public Sub New()
        InitializeComponent()
        LoadKemasanMaster()
        SetupForm()
    End Sub

    Public Sub New(editData As KemasanDetailDTO)
        InitializeComponent()
        LoadKemasanMaster()
        SetupForm()
        
        _isEditMode = True
        KemasanDetailData = editData
        LoadDataToForm()
    End Sub

    Private Sub LoadKemasanMaster()
        Try
            Using dc As New dxjbt2Entities
                dc.Configuration.LazyLoadingEnabled = False
                _kemasanMasterList = dc.tm_kemasan.Where(Function(k) k.IsActive = True) _
                                                 .OrderBy(Function(k) k.Kode<PERSON>) _
                                                 .ToList()

                ' Setup lookup edit
                cboKemasan.Properties.DataSource = _kemasanMasterList
                cboKemasan.Properties.DisplayMember = "DisplayText"
                cboKemasan.Properties.ValueMember = "Id"
                cboKemasan.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup
                cboKemasan.Properties.SearchMode = DevExpress.XtraEditors.Controls.SearchMode.AutoComplete
                cboKemasan.Properties.NullText = "-- Pilih Jenis Kemasan --"
            End Using
        Catch ex As Exception
            MessageBox.Show("Error loading kemasan master: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetupForm()
        ' Set default values
        If Not _isEditMode Then
            KemasanDetailData = New KemasanDetailDTO()
            spinQty.Value = 1
            spinBeratSatuan.Value = 0
            lblTotalBerat.Text = "0.000 kg"
        End If

        ' Set form title
        Me.Text = If(_isEditMode, "Edit Kemasan", "Tambah Kemasan")
        btnSave.Text = If(_isEditMode, "Update", "Tambah")
    End Sub

    Private Sub LoadDataToForm()
        If KemasanDetailData IsNot Nothing Then
            cboKemasan.EditValue = KemasanDetailData.Kemasan_id
            spinQty.Value = KemasanDetailData.Qty
            spinBeratSatuan.Value = KemasanDetailData.BeratSatuanKemasan
            txtKeterangan.Text = KemasanDetailData.Keterangan
            HitungTotalBerat()
        End If
    End Sub

    Private Sub cboKemasan_EditValueChanged(sender As Object, e As EventArgs) Handles cboKemasan.EditValueChanged
        Try
            If cboKemasan.EditValue IsNot Nothing AndAlso IsNumeric(cboKemasan.EditValue) Then
                Dim kemasanId As Integer = CInt(cboKemasan.EditValue)
                Dim kemasan = _kemasanMasterList.FirstOrDefault(Function(k) k.Id = kemasanId)

                If kemasan IsNot Nothing Then
                    ' Auto-fill berat satuan
                    spinBeratSatuan.Value = kemasan.BeratKemasan
                    
                    ' Update info kemasan
                    lblInfoKemasan.Text = $"{kemasan.NamaKemasan} ({kemasan.UkuranKemasan})"
                    
                    ' Hitung total berat
                    HitungTotalBerat()
                End If
            Else
                lblInfoKemasan.Text = ""
                spinBeratSatuan.Value = 0
                HitungTotalBerat()
            End If
        Catch ex As Exception
            MessageBox.Show("Error selecting kemasan: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End Try
    End Sub

    Private Sub spinQty_EditValueChanged(sender As Object, e As EventArgs) Handles spinQty.EditValueChanged
        HitungTotalBerat()
    End Sub

    Private Sub spinBeratSatuan_EditValueChanged(sender As Object, e As EventArgs) Handles spinBeratSatuan.EditValueChanged
        HitungTotalBerat()
    End Sub

    Private Sub HitungTotalBerat()
        Try
            Dim qty As Integer = CInt(spinQty.Value)
            Dim beratSatuan As Decimal = CDec(spinBeratSatuan.Value)
            Dim totalBerat As Decimal = qty * beratSatuan
            
            lblTotalBerat.Text = $"{totalBerat:N3} kg"
        Catch ex As Exception
            lblTotalBerat.Text = "0.000 kg"
        End Try
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If ValidateForm() Then
            SaveData()
            Me.DialogResult = DialogResult.OK
            Me.Close()
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        ' Validate kemasan selection
        If cboKemasan.EditValue Is Nothing OrElse Not IsNumeric(cboKemasan.EditValue) Then
            MessageBox.Show("Pilih jenis kemasan!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cboKemasan.Focus()
            Return False
        End If

        ' Validate qty
        If spinQty.Value <= 0 Then
            MessageBox.Show("Qty harus lebih dari 0!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            spinQty.Focus()
            Return False
        End If

        ' Validate berat satuan
        If spinBeratSatuan.Value < 0 Then
            MessageBox.Show("Berat satuan tidak boleh negatif!", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            spinBeratSatuan.Focus()
            Return False
        End If

        Return True
    End Function

    Private Sub SaveData()
        Try
            ' Update DTO with form data
            KemasanDetailData.Kemasan_id = CInt(cboKemasan.EditValue)
            KemasanDetailData.Qty = CInt(spinQty.Value)
            KemasanDetailData.BeratSatuanKemasan = CDec(spinBeratSatuan.Value)
            KemasanDetailData.TotalBeratKemasan = KemasanDetailData.Qty * KemasanDetailData.BeratSatuanKemasan
            KemasanDetailData.Keterangan = txtKeterangan.Text.Trim()

            ' Set kemasan info for display
            Dim kemasan = _kemasanMasterList.FirstOrDefault(Function(k) k.Id = KemasanDetailData.Kemasan_id)
            If kemasan IsNot Nothing Then
                KemasanDetailData.KodeKemasan = kemasan.KodeKemasan
                KemasanDetailData.NamaKemasan = kemasan.NamaKemasan
                KemasanDetailData.UkuranKemasan = kemasan.UkuranKemasan
            End If

            ' Set audit fields
            If Not _isEditMode Then
                KemasanDetailData.CreatedDate = DateTime.Now
                KemasanDetailData.CreatedBy = SecurityHelper.GetCurrentUsername()
            End If

        Catch ex As Exception
            MessageBox.Show("Error saving data: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub frmKemasanEntry_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Focus ke kemasan combo box
        cboKemasan.Focus()
    End Sub
End Class
