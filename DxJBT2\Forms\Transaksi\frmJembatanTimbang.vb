Imports DxJBT2

Public Class frmJembatanTimbang
    Inherits frmBaseList

    Private Shared ThisForm As frmJembatanTimbang

    Public Sub New()
        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        refreshData("")
    End Sub

    Public Function CreateInstance() As frmJembatanTimbang
        Dim f As frmJembatanTimbang
        ThisForm = Nothing
        If ThisForm Is Nothing Then
            ThisForm = New frmJembatanTimbang
        End If
        f = DirectCast(ThisForm, frmJembatanTimbang)
        Return f
    End Function

    Public Sub DbAction(msg As String)
        Select Case msg.Trim.ToUpper
            Case "ADD"
                addRecord()
            Case "EDIT"
                editRecord()
            Case "VIEW"
                viewRecord()
            Case "DELETE"
                DeleteRecord()
            Case "PRINT"
                PrintSlip()
            Case Else
                Close()
        End Select
    End Sub

    Private Sub editRecord()
        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
        If o Is Nothing Then
            Return
        End If

        frmJembatanTimbang_edit.showAs_Edit(New frmJembatanTimbang_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub refreshData(ByVal s As String)
        Dim dc As New dxjbt2Entities
        Dim q = From c In dc.tr_jembatan_timbang Order By c.TanggalMasuk Descending Select c
        TrjembatantimbangBindingSource.DataSource = q.ToList
    End Sub

    Private Sub addRecord()
        frmJembatanTimbang_edit.showAs_Add(New frmJembatanTimbang_edit.MsgHandler(AddressOf refreshData))
    End Sub

    Private Sub viewRecord()
        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
        If o Is Nothing Then
            Return
        End If

        frmJembatanTimbang_edit.showAs_View(New frmJembatanTimbang_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub DeleteRecord()
        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
        If o Is Nothing Then
            Return
        End If

        Dim dr As DialogResult = MessageBox.Show("Are you sure you want to delete this transaction?" & vbCrLf & _
                                               "No. Transaksi: " & o.NoTransaksi & vbCrLf & _
                                               "Tanggal: " & o.TanggalMasuk.ToString("dd/MM/yyyy HH:mm") & vbCrLf & _
                                               "No. Polisi: " & o.NoPolisi, _
                                               "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

        If dr = DialogResult.Yes Then
            Try
                Dim dc As New dxjbt2Entities
                Dim q = From c In dc.tr_jembatan_timbang Where c.Id = o.Id
                dc.tr_jembatan_timbang.Remove(q.FirstOrDefault)
                dc.SaveChanges()
                refreshData("")
                MessageBox.Show("Transaction deleted successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("Delete Failed: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub PrintSlip()
        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
        If o Is Nothing Then
            MessageBox.Show("Pilih transaksi yang akan dicetak slip-nya", "Informasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' Check if transaction is completed (has both entry and exit weights)
        If Not o.BeratKeluar.HasValue OrElse o.BeratKeluar.Value <= 0 Then
            MessageBox.Show("Slip timbangan hanya dapat dicetak untuk transaksi yang sudah selesai (memiliki berat masuk dan keluar)", "Informasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Try
            ' Load complete transaction data with related entities
            Dim dc As New dxjbt2Entities
            Dim transaction = (From t In dc.tr_jembatan_timbang.Include("tm_kendaraan").Include("tm_mitra").Include("tr_kemasan_detail")
                              Where t.Id = o.Id
                              Select t).FirstOrDefault()

            If transaction IsNot Nothing Then
                ' Create slip data DTO to avoid context disposal issues
                Dim slipData = WeighingSlipReport.CreateSlipData(transaction)

                Dim result As DialogResult = MessageBox.Show(
                    "Print Slip Timbangan" & vbCrLf & vbCrLf &
                    "No. Transaksi: " & slipData.NoTransaksi & vbCrLf &
                    "No. Polisi: " & slipData.NoPolisi & vbCrLf &
                    "Berat Netto: " & slipData.BeratNetto.Value.ToString("#,##0") & " kg" & vbCrLf & vbCrLf &
                    "Klik Yes untuk preview, No untuk print langsung",
                    "Print Slip Timbangan",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question)

                If result = DialogResult.Yes Then
                    ' Create and show print preview
                    Dim slipReport As New WeighingSlipReport(slipData)
                    slipReport.PrintSlip()
                ElseIf result = DialogResult.No Then
                    ' Print directly without preview
                    Dim slipReport As New WeighingSlipReport(slipData)
                    slipReport.PrintDirectly()
                    MessageBox.Show("Slip timbangan telah dicetak!", "Print Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            Else
                MessageBox.Show("Data transaksi tidak ditemukan", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error saat mencetak slip: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
