Imports DxJBT2

Public Class frmMitra_edit
    Inherits frmEntryBase

    Private modRefreshData As MsgHandler
    Private dbAction As String

    Friend Shared Sub showAs_Add(msg As MsgHandler)
        Dim f = New frmMitra_edit
        With f
            .modRefreshData = msg
            .dbAction = "add"

            Dim dc As New dxjbt2Entities

            .TmmitraBindingSource.DataSource = (From c In dc.tm_mitra Where c.Id = 0 Select c).ToList
            .TmmitraBindingSource.AddNew()

            .ShowDialog()
        End With
    End Sub

    Private Sub frmMitra_edit_Activated(sender As Object, e As EventArgs) Handles Me.Activated
        KodeMitraTextEdit.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If DxValidationProvider1.Validate = False Then
            Return
        End If
        If dbAction = "add" Then
            saveAdd()
        Else
            saveEdit()
        End If
        Close()
    End Sub

    Private Sub saveEdit()
        Dim dc As New dxjbt2Entities
        Try
            Dim o = CType(TmmitraBindingSource.Current, tm_mitra)
            Dim q = From c In dc.tm_mitra Where c.Id = o.Id
            With q.FirstOrDefault
                .KodeMitra = o.KodeMitra
                .NamaMitra = o.NamaMitra
                .Alamat = o.Alamat
                .Kota = o.Kota
                .Telp = o.Telp
                .TipeMitra = o.TipeMitra
            End With
            dc.SaveChanges()
            modRefreshData(o.Id.ToString)
            MessageBox.Show("Successfully Updated!", "Update", MessageBoxButtons.OK, MessageBoxIcon.Question)
            isClosing = True
        Catch ex As Exception
            MessageBox.Show("Update Failed! : " & ex.Message, "Update", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Friend Shared Sub showAs_Edit(msg As MsgHandler, id As Integer)
        Dim f = New frmMitra_edit
        With f
            .modRefreshData = msg
            .dbAction = "edit"

            Dim dc As New dxjbt2Entities
            .TmmitraBindingSource.DataSource = (From c In dc.tm_mitra Where c.Id = id Select c).ToList

            .btnSave.Text = "Update"
            .btnCancel.Text = "Close"
            .ShowDialog()
        End With
    End Sub

    Friend Shared Sub showAs_View(msg As MsgHandler, id As Integer)
        Dim f = New frmMitra_edit
        With f
            .modRefreshData = msg
            .dbAction = "view"

            Dim dc As New dxjbt2Entities
            .TmmitraBindingSource.DataSource = (From c In dc.tm_mitra Where c.Id = id Select c).ToList

            ' Disable all edit controls
            .KodeMitraTextEdit.ReadOnly = True
            .NamaMitraTextEdit.ReadOnly = True
            .AlamatMemoEdit.ReadOnly = True
            .KotaTextEdit.ReadOnly = True
            .TelpTextEdit.ReadOnly = True
            .TipeMitraComboBoxEdit.Properties.ReadOnly = True

            ' Hide save button and change cancel button text
            .btnSave.Visible = False
            .btnCancel.Text = "Close"

            .ShowDialog()
        End With
    End Sub

    Private Sub saveAdd()
        Dim dc As New dxjbt2Entities
        Try
            Dim o = CType(TmmitraBindingSource.Current, tm_mitra)
            dc.tm_mitra.Add(o)
            dc.SaveChanges()
            modRefreshData(o.Id.ToString)
            Dim dr As DialogResult = MessageBox.Show("Successfully Saved!" & vbCrLf & "Do you want to add another data?", "Save", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
            If dr = Windows.Forms.DialogResult.Yes Then
                TmmitraBindingSource.AddNew()
                KodeMitraTextEdit.Focus()
            Else
                isClosing = True
            End If
        Catch ex As Exception
            MessageBox.Show("Save Failed! : " & ex.Message, "Save", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class