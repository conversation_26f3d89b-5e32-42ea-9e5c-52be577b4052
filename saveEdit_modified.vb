    Private Sub saveEdit()
        Try
            ' Get the current entity from binding source
            Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)

            ' Validate required fields before proceeding
            If String.IsNullOrEmpty(o<PERSON>NoTransaksi) Then
                MessageBox.Show("Nomor Transaksi tidak boleh kosong", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                NoTransaksiTextEdit.Focus()
                Return
            End If

            If o.Tanggal<PERSON>uk = Nothing Then
                MessageBox.Show("Tanggal Masuk tidak boleh kosong", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                TanggalMasukDateEdit.Focus()
                Return
            End If

            ' Validate <PERSON><PERSON><PERSON><PERSON><PERSON> must be greater than 0
            If <PERSON><PERSON> <= 0 Then
                MessageBox.Show("Berat Masuk harus lebih besar dari 0", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                BeratMasukTextEdit.Focus()
                Return
            End If

            ' Create a new context for this operation
