﻿Public Class frmTipeKendaraan
    Inherits frmBaseList

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        refreshData("")

    End Sub

    Public Function CreateInstance() As frmTipeKendaraan
        Dim f As frmTipeKendaraan
        ThisForm = Nothing
        If ThisForm Is Nothing Then
            ThisForm = New frmTipeKendaraan
        End If
        f = DirectCast(ThisForm, frmTipeKendaraan)
        Return f
    End Function
    Public Sub DbAction(ByVal msg As String)
        Select Case msg.Trim.ToUpper
            Case "ADD"
                addRecord()
            Case "EDIT"
                editRecord()
            Case "VIEW"
                viewRecord()
            Case "DELETE"
                DeleteRecord()
            Case Else
                Close()
        End Select
    End Sub
    Private Sub refreshData(ByVal s As String)
        Dim dc As New dxjbt2Entities
        Dim q = From c In dc.tm_tipe_kendaraan Order By c.NamaTipe Select c
        TmtipekendaraanBindingSource.DataSource = q.ToList
    End Sub
    Private Sub addRecord()

        frmTipeKendaraan_Edit.showAs_Add(New frmTipeKendaraan_Edit.MsgHandler(AddressOf refreshData))

    End Sub
    Private Sub editRecord()
        Dim tTipe As tm_tipe_kendaraan = CType(TmtipekendaraanBindingSource.Current, tm_tipe_kendaraan)
        If tTipe Is Nothing Then
            Return
        End If

        frmTipeKendaraan_Edit.showAs_Edit(New frmTipeKendaraan_Edit.MsgHandler(AddressOf refreshData), tTipe.Id)
    End Sub
    Private Sub viewRecord()
        Dim tTipe As tm_tipe_kendaraan = CType(TmtipekendaraanBindingSource.Current, tm_tipe_kendaraan)
        If tTipe Is Nothing Then
            Return
        End If

        frmTipeKendaraan_Edit.showAs_View(New frmTipeKendaraan_Edit.MsgHandler(AddressOf refreshData), tTipe.Id)
    End Sub
    Private Sub DeleteRecord()
        Dim tTipe As tm_tipe_kendaraan = CType(TmtipekendaraanBindingSource.Current, tm_tipe_kendaraan)
        If tTipe Is Nothing Then
            Return
        End If
        Dim dr As DialogResult = MessageBox.Show("Are you sure you want to delete this record?" & vbCrLf &
                                                 "Nama Tipe : " + tTipe.NamaTipe, "About to Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
        If dr = Windows.Forms.DialogResult.Yes Then
            Try
                Dim dc As New dxjbt2Entities
                Dim q = From c In dc.tm_tipe_kendaraan Where c.Id = tTipe.Id
                dc.tm_tipe_kendaraan.Remove(q.FirstOrDefault)
                dc.SaveChanges()
                refreshData("")
            Catch ex As Exception
                MessageBox.Show("Delete Failed : " & ex.Message)
            End Try
        End If
    End Sub
End Class