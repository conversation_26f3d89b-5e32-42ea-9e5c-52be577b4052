-- Menambahkan kolom untuk total berat kemasan dan berat netto murni
-- ke tabel tr_jembatan_timbang

ALTER TABLE tr_jembatan_timbang 
ADD TotalBeratKemasan DECIMAL(10,3) NULL DEFAULT 0;

ALTER TABLE tr_jembatan_timbang 
ADD BeratNettoMurni DECIMAL(10,3) NULL;

-- Menambahkan comment untuk kolom baru
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Total berat semua kemasan dalam transaksi ini', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'tr_jembatan_timbang', 
    @level2type = N'COLUMN', @level2name = N'TotalBeratKemasan';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Berat netto murni setelah dikurangi berat kemasan (BeratNetto - TotalBeratKemasan)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'tr_jembatan_timbang', 
    @level2type = N'COLUMN', @level2name = N'BeratNettoMurni';

-- Membuat view untuk kemudahan query
CREATE VIEW vw_jembatan_timbang_with_kemasan AS
SELECT 
    jt.*,
    ISNULL(kemasan_summary.TotalQtyKemasan, 0) as TotalQtyKemasan,
    ISNULL(kemasan_summary.JumlahJenisKemasan, 0) as JumlahJenisKemasan,
    CASE 
        WHEN jt.BeratNetto IS NOT NULL AND jt.TotalBeratKemasan IS NOT NULL 
        THEN jt.BeratNetto - jt.TotalBeratKemasan 
        ELSE NULL 
    END as BeratNettoMurniCalculated
FROM tr_jembatan_timbang jt
LEFT JOIN (
    SELECT 
        JembatanTimbang_id,
        SUM(Qty) as TotalQtyKemasan,
        SUM(TotalBeratKemasan) as TotalBeratKemasanFromDetail,
        COUNT(*) as JumlahJenisKemasan
    FROM tr_kemasan_detail 
    GROUP BY JembatanTimbang_id
) kemasan_summary ON jt.Id = kemasan_summary.JembatanTimbang_id;
