Imports System.Data.Entity
Imports DxJBT2.Helpers

Public Class frmUser
    Private _context As New dxjbt2Entities()
    Private _isNewRecord As Boolean = False
    Private _currentId As Integer = 0

    Private Sub frmUser_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        SetupForm()
        LoadRoles()
        RefreshGrid()
    End Sub

    Private Sub SetupForm()
        ' Setup DataGridView
        dgvUser.AutoGenerateColumns = False
        dgvUser.Columns.Clear()

        ' Tambahkan kolom
        Dim idColumn As New DataGridViewTextBoxColumn()
        idColumn.DataPropertyName = "Id"
        idColumn.HeaderText = "ID"
        idColumn.Visible = False
        dgvUser.Columns.Add(idColumn)

        Dim usernameColumn As New DataGridViewTextBoxColumn()
        usernameColumn.DataPropertyName = "Username"
        usernameColumn.HeaderText = "Username"
        usernameColumn.Width = 100
        dgvUser.Columns.Add(usernameColumn)

        Dim namaColumn As New DataGridViewTextBoxColumn()
        namaColumn.DataPropertyName = "NamaLengkap"
        namaColumn.HeaderText = "Nama Lengkap"
        namaColumn.Width = 150
        dgvUser.Columns.Add(namaColumn)

        Dim emailColumn As New DataGridViewTextBoxColumn()
        emailColumn.DataPropertyName = "Email"
        emailColumn.HeaderText = "Email"
        emailColumn.Width = 150
        dgvUser.Columns.Add(emailColumn)

        Dim roleColumn As New DataGridViewTextBoxColumn()
        roleColumn.DataPropertyName = "tm_role.NamaRole"
        roleColumn.HeaderText = "Role"
        roleColumn.Width = 100
        dgvUser.Columns.Add(roleColumn)

        Dim statusColumn As New DataGridViewCheckBoxColumn()
        statusColumn.DataPropertyName = "IsActive"
        statusColumn.HeaderText = "Aktif"
        statusColumn.Width = 50
        dgvUser.Columns.Add(statusColumn)

        ' Set mode awal
        SetFormMode(False)
    End Sub

    Private Sub LoadRoles()
        Try
            cboRole.DataSource = _context.tm_role.Where(Function(r) r.IsActive = True).ToList()
            cboRole.DisplayMember = "NamaRole"
            cboRole.ValueMember = "Id"
        Catch ex As Exception
            MessageBox.Show($"Error saat memuat data role: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub RefreshGrid()
        Try
            ' Gunakan Include untuk memuat data role
            dgvUser.DataSource = _context.tm_user.Include("tm_role").ToList()
        Catch ex As Exception
            MessageBox.Show($"Error saat memuat data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(isEdit As Boolean)
        ' Atur mode form
        txtUsername.Enabled = isEdit And _isNewRecord ' Username hanya bisa diedit saat baru
        txtNamaLengkap.Enabled = isEdit
        txtEmail.Enabled = isEdit
        txtNoTelp.Enabled = isEdit
        txtPassword.Enabled = isEdit
        cboRole.Enabled = isEdit
        chkActive.Enabled = isEdit

        ' Atur tombol
        btnNew.Enabled = Not isEdit
        btnEdit.Enabled = Not isEdit And dgvUser.SelectedRows.Count > 0
        btnDelete.Enabled = Not isEdit And dgvUser.SelectedRows.Count > 0
        btnSave.Enabled = isEdit
        btnCancel.Enabled = isEdit
        btnResetPassword.Enabled = Not isEdit And dgvUser.SelectedRows.Count > 0

        ' Clear textbox jika mode baru
        If isEdit And _isNewRecord Then
            txtUsername.Text = ""
            txtNamaLengkap.Text = ""
            txtEmail.Text = ""
            txtNoTelp.Text = ""
            txtPassword.Text = ""
            chkActive.Checked = True
            If cboRole.Items.Count > 0 Then
                cboRole.SelectedIndex = 0
            End If
        End If
    End Sub

    Private Sub LoadDataToForm(id As Integer)
        Try
            Dim user = _context.tm_user.Find(id)
            If user IsNot Nothing Then
                _currentId = user.Id
                txtUsername.Text = user.Username
                txtNamaLengkap.Text = user.NamaLengkap
                txtEmail.Text = user.Email
                txtNoTelp.Text = user.NoTelp
                txtPassword.Text = "********" ' Password tidak ditampilkan
                chkActive.Checked = user.IsActive
                cboRole.SelectedValue = user.Role_id
            End If
        Catch ex As Exception
            MessageBox.Show($"Error saat memuat data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvUser_SelectionChanged(sender As Object, e As EventArgs) Handles dgvUser.SelectionChanged
        If dgvUser.SelectedRows.Count > 0 Then
            Try
                ' Menggunakan indeks kolom 0 yang merupakan kolom Id
                Dim id As Integer = CInt(dgvUser.SelectedRows(0).Cells(0).Value)
                LoadDataToForm(id)
                btnEdit.Enabled = True
                btnDelete.Enabled = True
                btnResetPassword.Enabled = True
            Catch ex As Exception
                ' Handle error saat mengakses kolom Id
                MessageBox.Show($"Error saat memilih data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                btnEdit.Enabled = False
                btnDelete.Enabled = False
                btnResetPassword.Enabled = False
            End Try
        Else
            btnEdit.Enabled = False
            btnDelete.Enabled = False
            btnResetPassword.Enabled = False
        End If
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        _isNewRecord = True
        _currentId = 0
        SetFormMode(True)
        txtUsername.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If dgvUser.SelectedRows.Count > 0 Then
            _isNewRecord = False
            SetFormMode(True)
            txtNamaLengkap.Focus()
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If ValidateForm() Then
            Try
                If _isNewRecord Then
                    ' Tambah baru
                    Dim newUser As New tm_user() With {
                        .Username = txtUsername.Text.Trim(),
                        .Password = PasswordHelper.EncryptPassword(txtPassword.Text.Trim()), ' Enkripsi password
                        .NamaLengkap = txtNamaLengkap.Text.Trim(),
                        .Email = txtEmail.Text.Trim(),
                        .NoTelp = txtNoTelp.Text.Trim(),
                        .Role_id = CInt(cboRole.SelectedValue),
                        .IsActive = chkActive.Checked,
                        .LastUpdate = DateTime.Now
                    }
                    _context.tm_user.Add(newUser)
                Else
                    ' Update
                    Dim user = _context.tm_user.Find(_currentId)
                    If user IsNot Nothing Then
                        user.NamaLengkap = txtNamaLengkap.Text.Trim()
                        user.Email = txtEmail.Text.Trim()
                        user.NoTelp = txtNoTelp.Text.Trim()
                        user.Role_id = CInt(cboRole.SelectedValue)
                        user.IsActive = chkActive.Checked
                        user.LastUpdate = DateTime.Now

                        ' Update password jika diubah (tidak sama dengan placeholder)
                        If txtPassword.Text <> "********" Then
                            user.Password = PasswordHelper.EncryptPassword(txtPassword.Text.Trim()) ' Enkripsi password
                        End If
                    End If
                End If

                _context.SaveChanges()
                MessageBox.Show("Data berhasil disimpan", "Informasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
                RefreshGrid()
                SetFormMode(False)
            Catch ex As Exception
                MessageBox.Show($"Error saat menyimpan data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("Username harus diisi", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtNamaLengkap.Text) Then
            MessageBox.Show("Nama Lengkap harus diisi", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNamaLengkap.Focus()
            Return False
        End If

        If _isNewRecord And String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("Password harus diisi", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return False
        End If

        If cboRole.SelectedIndex = -1 Then
            MessageBox.Show("Role harus dipilih", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cboRole.Focus()
            Return False
        End If

        ' Validasi username unik
        If _isNewRecord Then
            Dim existingUser = _context.tm_user.FirstOrDefault(Function(u) u.Username = txtUsername.Text.Trim())
            If existingUser IsNot Nothing Then
                MessageBox.Show("Username sudah digunakan", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtUsername.Focus()
                Return False
            End If
        End If

        Return True
    End Function

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If dgvUser.SelectedRows.Count > 0 Then
            Try
                ' Menggunakan indeks kolom 0 yang merupakan kolom Id
                Dim id As Integer = CInt(dgvUser.SelectedRows(0).Cells(0).Value)

                If MessageBox.Show("Apakah Anda yakin ingin menghapus data ini?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    Try
                        Dim user = _context.tm_user.Find(id)
                        If user IsNot Nothing Then
                            _context.tm_user.Remove(user)
                            _context.SaveChanges()
                            MessageBox.Show("Data berhasil dihapus", "Informasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
                            RefreshGrid()
                        End If
                    Catch ex As Exception
                        MessageBox.Show($"Error saat menghapus data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End Try
                End If
            Catch ex As Exception
                MessageBox.Show($"Error saat memilih data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        SetFormMode(False)
        If dgvUser.SelectedRows.Count > 0 Then
            Try
                ' Menggunakan indeks kolom 0 yang merupakan kolom Id
                Dim id As Integer = CInt(dgvUser.SelectedRows(0).Cells(0).Value)
                LoadDataToForm(id)
            Catch ex As Exception
                MessageBox.Show($"Error saat memuat data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub btnResetPassword_Click(sender As Object, e As EventArgs) Handles btnResetPassword.Click
        If dgvUser.SelectedRows.Count > 0 Then
            Try
                ' Menggunakan indeks kolom 0 yang merupakan kolom Id
                Dim id As Integer = CInt(dgvUser.SelectedRows(0).Cells(0).Value)

                If MessageBox.Show("Apakah Anda yakin ingin mereset password user ini?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    Try
                        Dim user = _context.tm_user.Find(id)
                        If user IsNot Nothing Then
                            ' Reset password ke default dengan enkripsi
                            Dim defaultPassword = PasswordHelper.GenerateDefaultPassword()
                            user.Password = PasswordHelper.EncryptPassword(defaultPassword)
                            user.LastUpdate = DateTime.Now
                            _context.SaveChanges()
                            MessageBox.Show($"Password berhasil direset menjadi: {defaultPassword}", "Informasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
                        End If
                    Catch ex As Exception
                        MessageBox.Show($"Error saat mereset password: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End Try
                End If
            Catch ex As Exception
                MessageBox.Show($"Error saat memilih data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class
