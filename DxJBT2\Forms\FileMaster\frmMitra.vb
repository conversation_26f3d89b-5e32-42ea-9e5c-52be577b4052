Imports DxJBT2

Public Class frmMitra
    Inherits frmBaseList

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add TipeMitra column programmatically
        AddTipeMitraColumn()

        ' Add any initialization after the InitializeComponent() call.
        refreshData("")
    End Sub

    Private Sub AddTipeMitraColumn()
        ' Create new TipeMitra column
        Dim colTipeMitra As New DevExpress.XtraGrid.Columns.GridColumn()
        colTipeMitra.FieldName = "TipeMitra"
        colTipeMitra.MinWidth = 25
        colTipeMitra.Name = "colTipeMitra"
        colTipeMitra.Visible = True
        colTipeMitra.VisibleIndex = 5
        colTipeMitra.Width = 94
        colTipeMitra.Caption = "Tipe Mitra"

        ' Add column to grid
        GridView1.Columns.Add(colTipeMitra)
    End Sub

    Public Function CreateInstance() As frmMitra
        Dim f As frmMitra
        ThisForm = Nothing
        If ThisForm Is Nothing Then
            ThisForm = New frmMitra
        End If
        f = DirectCast(ThisForm, frmMitra)
        Return f
    End Function

    Public Sub DbAction(msg As String)
        Select Case msg.Trim.ToUpper
            Case "ADD"
                addRecord()
            Case "EDIT"
                editRecord()
            Case "VIEW"
                viewRecord()
            Case "DELETE"
                DeleteRecord()
            Case Else
                Close()
        End Select
    End Sub

    Private Sub editRecord()
        Dim o = CType(TmmitraBindingSource.Current, tm_mitra)
        If o Is Nothing Then
            Return
        End If

        frmMitra_edit.showAs_Edit(New frmMitra_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub refreshData(ByVal s As String)
        Dim dc As New dxjbt2Entities
        Dim q = From c In dc.tm_mitra Order By c.KodeMitra Select c
        TmmitraBindingSource.DataSource = q.ToList
    End Sub

    Private Sub addRecord()
        frmMitra_edit.showAs_Add(New frmMitra_edit.MsgHandler(AddressOf refreshData))
    End Sub

    Private Sub viewRecord()
        Dim o = CType(TmmitraBindingSource.Current, tm_mitra)
        If o Is Nothing Then
            Return
        End If

        frmMitra_edit.showAs_View(New frmMitra_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub DeleteRecord()
        Dim o = CType(TmmitraBindingSource.Current, tm_mitra)
        If o Is Nothing Then
            Return
        End If

        Dim dr As DialogResult = MessageBox.Show("Are you sure you want to delete this partner?" & vbCrLf & _
                                               "Code: " & o.KodeMitra & vbCrLf & _
                                               "Name: " & o.NamaMitra, _
                                               "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

        If dr = DialogResult.Yes Then
            Try
                Dim dc As New dxjbt2Entities
                Dim q = From c In dc.tm_mitra Where c.Id = o.Id
                dc.tm_mitra.Remove(q.FirstOrDefault)
                dc.SaveChanges()
                refreshData("")
                MessageBox.Show("Partner deleted successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("Delete Failed: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class