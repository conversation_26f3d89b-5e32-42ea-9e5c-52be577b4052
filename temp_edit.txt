            If o.<PERSON> = Nothing Then
                MessageBox.Show("Tanggal Masuk tidak boleh kosong", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                TanggalMasukDateEdit.Focus()
                Return
            End If

            ' Validate <PERSON><PERSON><PERSON><PERSON> must be greater than 0
            If o.<PERSON><PERSON> <= 0 Then
                MessageBox.Show("Berat Masuk harus lebih besar dari 0", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                BeratMasukTextEdit.Focus()
                Return
            End If

            ' Create a new context for this operation
