Imports System.Data.Entity
Imports System.Linq
Imports DxJBT2.Helpers

Public Class MenuHelper
    ' Class untuk menyimpan informasi menu item
    Public Class MenuItemInfo
        Public Property MenuId As Integer
        Public Property KodeMenu As String
        Public Property NamaMenu As String
        Public Property FormName As String
        Public Property MenuIcon As String
        Public Property ParentMenuId As Integer?
        Public Property MenuOrder As Integer
        Public Property CanView As Boolean
        Public Property CanAdd As Boolean
        Public Property CanEdit As Boolean
        Public Property CanDelete As Boolean
        Public Property CanPrint As Boolean
        Public Property Children As List(Of MenuItemInfo)

        Public Sub New()
            Children = New List(Of MenuItemInfo)()
        End Sub
    End Class

    ' Fungsi untuk mendapatkan struktur menu hierarki untuk user yang sedang login
    Public Shared Function GetUserMenuHierarchy() As List(Of MenuItemInfo)
        Dim userMenus = SecurityHelper.GetUserMenus()
        Dim menuItems = New List(Of MenuItemInfo)()

        ' Convert ke MenuItemInfo dengan data lengkap dari tm_menu
        Try
            Using context As New dxjbt2Entities()
                For Each menu In userMenus
                    ' Ambil data lengkap dari tm_menu untuk mendapatkan MenuIcon dan ParentMenu_id
                    Dim dbMenu = context.tm_menu.FirstOrDefault(Function(m) m.Id = menu.MenuId)

                    Dim menuItem = New MenuItemInfo With {
                        .MenuId = menu.MenuId,
                        .KodeMenu = menu.KodeMenu,
                        .NamaMenu = menu.NamaMenu,
                        .FormName = menu.FormName,
                        .MenuIcon = If(dbMenu IsNot Nothing, dbMenu.MenuIcon, ""),
                        .ParentMenuId = If(dbMenu IsNot Nothing, dbMenu.ParentMenu_id, Nothing),
                        .MenuOrder = menu.MenuOrder,
                        .CanView = menu.CanView = 1,
                        .CanAdd = menu.CanAdd = 1,
                        .CanEdit = menu.CanEdit = 1,
                        .CanDelete = menu.CanDelete = 1,
                        .CanPrint = menu.CanPrint = 1
                    }

                    menuItems.Add(menuItem)
                Next
            End Using
        Catch ex As Exception
            ' Jika error, fallback tanpa icon dan parent
            For Each menu In userMenus
                Dim menuItem = New MenuItemInfo With {
                    .MenuId = menu.MenuId,
                    .KodeMenu = menu.KodeMenu,
                    .NamaMenu = menu.NamaMenu,
                    .FormName = menu.FormName,
                    .MenuIcon = "",
                    .ParentMenuId = Nothing,
                    .MenuOrder = menu.MenuOrder,
                    .CanView = menu.CanView = 1,
                    .CanAdd = menu.CanAdd = 1,
                    .CanEdit = menu.CanEdit = 1,
                    .CanDelete = menu.CanDelete = 1,
                    .CanPrint = menu.CanPrint = 1
                }
                menuItems.Add(menuItem)
            Next
        End Try

        ' Buat struktur hierarki
        Return BuildMenuHierarchy(menuItems)
    End Function

    ' Fungsi untuk membangun struktur hierarki menu
    Private Shared Function BuildMenuHierarchy(menuItems As List(Of MenuItemInfo)) As List(Of MenuItemInfo)
        Dim rootMenus = New List(Of MenuItemInfo)()

        ' Ambil menu root (yang tidak punya parent)
        Dim parentMenus = menuItems.Where(Function(m) Not m.ParentMenuId.HasValue).OrderBy(Function(m) m.MenuOrder).ToList()

        For Each parentMenu In parentMenus
            ' Cari child menu untuk setiap parent
            Dim childMenus = menuItems.Where(Function(m) m.ParentMenuId.HasValue AndAlso m.ParentMenuId.Value = parentMenu.MenuId).
                OrderBy(Function(m) m.MenuOrder).ToList()

            parentMenu.Children.AddRange(childMenus)
            rootMenus.Add(parentMenu)
        Next

        Return rootMenus
    End Function

    ' Fungsi untuk membuat menu strip secara dinamis
    Public Shared Sub BuildMenuStrip(menuStrip As MenuStrip)
        Try
            ' Clear existing menu items (kecuali yang system)
            Dim itemsToRemove = New List(Of ToolStripMenuItem)()
            ' Convert to local variable to avoid VB.NET For Each property issue
            Dim menuItemList = menuStrip.Items.Cast(Of ToolStripMenuItem)().ToList()
            For Each item As ToolStripMenuItem In menuItemList
                If item.Tag IsNot Nothing AndAlso item.Tag.ToString() = "DYNAMIC" Then
                    itemsToRemove.Add(item)
                End If
            Next

            For Each item In itemsToRemove
                menuStrip.Items.Remove(item)
            Next

            ' Dapatkan menu hierarchy
            Dim menuHierarchy = GetUserMenuHierarchy()

            ' Buat menu items
            For Each rootMenu In menuHierarchy
                Dim menuItem = CreateMenuItem(rootMenu)
                menuItem.Tag = "DYNAMIC" ' Mark sebagai dynamic menu
                menuStrip.Items.Add(menuItem)
            Next

        Catch ex As Exception
            MessageBox.Show($"Error building menu: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Fungsi untuk membuat menu item
    Private Shared Function CreateMenuItem(menuInfo As MenuItemInfo) As ToolStripMenuItem
        Dim menuItem = New ToolStripMenuItem(menuInfo.NamaMenu)
        menuItem.Tag = menuInfo.KodeMenu
        menuItem.Name = $"menu_{menuInfo.KodeMenu}"

        ' Set icon jika ada
        If Not String.IsNullOrEmpty(menuInfo.MenuIcon) Then
            ' Anda bisa menambahkan logic untuk load icon dari resource atau file
            ' menuItem.Image = LoadMenuIcon(menuInfo.MenuIcon)
        End If

        ' Jika ada form name, tambahkan click handler
        If Not String.IsNullOrEmpty(menuInfo.FormName) Then
            AddHandler menuItem.Click, Sub(sender, e) OpenForm(menuInfo.FormName, menuInfo)
        End If

        ' Tambahkan child menu jika ada
        For Each childMenu In menuInfo.Children
            Dim childMenuItem = CreateMenuItem(childMenu)
            menuItem.DropDownItems.Add(childMenuItem)
        Next

        Return menuItem
    End Function

    ' Fungsi untuk membuka form berdasarkan nama form
    Private Shared Sub OpenForm(formName As String, menuInfo As MenuItemInfo)
        Try
            ' Cek apakah user punya akses view
            If Not menuInfo.CanView Then
                MessageBox.Show("Anda tidak memiliki akses untuk membuka menu ini.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' Cari form yang sudah terbuka
            Dim existingForm = Application.OpenForms.Cast(Of Form)().FirstOrDefault(Function(f) f.GetType().Name = formName)
            If existingForm IsNot Nothing Then
                existingForm.BringToFront()
                existingForm.WindowState = FormWindowState.Normal
                Return
            End If

            ' Buat instance form baru menggunakan reflection
            Dim formType = Type.GetType($"DxJBT2.{formName}")
            If formType Is Nothing Then
                ' Coba cari di namespace lain
                Dim assembly = System.Reflection.Assembly.GetExecutingAssembly()
                formType = assembly.GetTypes().FirstOrDefault(Function(t) t.Name = formName)
            End If

            If formType IsNot Nothing Then
                Dim newForm = Activator.CreateInstance(formType)
                
                ' Set menu access information jika form mendukung
                If TypeOf newForm Is Form Then
                    Dim form = DirectCast(newForm, Form)
                    
                    ' Set tag dengan informasi menu access
                    form.Tag = menuInfo
                    
                    ' Jika form punya property untuk set access rights, set di sini
                    SetFormAccessRights(form, menuInfo)
                    
                    form.Show()
                End If
            Else
                MessageBox.Show($"Form '{formName}' tidak ditemukan.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show($"Error membuka form '{formName}': {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Fungsi untuk set access rights pada form
    Private Shared Sub SetFormAccessRights(form As Form, menuInfo As MenuItemInfo)
        Try
            ' Cari button/control yang berhubungan dengan access rights
            SetControlAccess(form, menuInfo.CanAdd, "btnAdd", "btnNew", "btnTambah")
            SetControlAccess(form, menuInfo.CanEdit, "btnEdit", "btnUbah", "btnModify")
            SetControlAccess(form, menuInfo.CanDelete, "btnDelete", "btnHapus", "btnRemove")
            SetControlAccess(form, menuInfo.CanPrint, "btnPrint", "btnCetak", "btnReport")

        Catch ex As Exception
            ' Ignore error jika control tidak ditemukan
        End Try
    End Sub

    ' Fungsi helper untuk set access control
    Private Shared Sub SetControlAccess(form As Form, enabled As Boolean, ParamArray controlNames As String())
        For Each controlName In controlNames
            Dim control = FindControlByName(form, controlName)
            If control IsNot Nothing Then
                ' Handle different control types
                If TypeOf control Is Control Then
                    DirectCast(control, Control).Enabled = enabled
                ElseIf TypeOf control Is ToolStripItem Then
                    DirectCast(control, ToolStripItem).Enabled = enabled
                End If
                Exit For
            End If
        Next
    End Sub

    ' Fungsi untuk mencari control berdasarkan nama
    Private Shared Function FindControlByName(parent As Control, controlName As String) As Object
        If parent.Name.Equals(controlName, StringComparison.OrdinalIgnoreCase) Then
            Return parent
        End If

        ' Convert to local variable to avoid VB.NET For Each property issue
        Dim controlList = parent.Controls.Cast(Of Control)().ToList()
        For Each child As Control In controlList
            Dim found = FindControlByName(child, controlName)
            If found IsNot Nothing Then
                Return found
            End If
        Next

        Return Nothing
    End Function
End Class
