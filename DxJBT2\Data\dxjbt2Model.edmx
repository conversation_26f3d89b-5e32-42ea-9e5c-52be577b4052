﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="dxjbt2Model.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="tm_kemasan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodeKemasan" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaKemasan" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="UkuranKemasan" Type="nvarchar" MaxLength="50" />
          <Property Name="BeratKemasan" Type="decimal" Precision="10" Scale="3" Nullable="false" />
          <Property Name="Satuan" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="Keterangan" Type="nvarchar" MaxLength="255" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="CreatedDate" Type="datetime" Nullable="false" />
          <Property Name="CreatedBy" Type="nvarchar" MaxLength="50" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="ModifiedBy" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="tm_kendaraan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NoPolisi" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="TipeKendaraan_id" Type="int" Nullable="false" />
          <Property Name="Mitra_id" Type="int" Nullable="false" />
          <Property Name="NamaSupir" Type="varchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_mitra">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodeMitra" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="NamaMitra" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Alamat" Type="varchar" MaxLength="250" />
          <Property Name="Kota" Type="varchar" MaxLength="50" />
          <Property Name="Telp" Type="varchar" MaxLength="50" />
          <Property Name="TipeMitra" Type="varchar" MaxLength="20" />
        </EntityType>
        <EntityType Name="tm_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="KodeRole" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaRole" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Deskripsi" Type="varchar" MaxLength="255" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="LastUpdate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_settings">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SettingName" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="SettingValue" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="Description" Type="varchar" MaxLength="255" />
          <Property Name="LastUpdate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="tm_tipe_kendaraan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NamaTipe" Type="varchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="tm_user">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Username" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="Password" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="NamaLengkap" Type="varchar" MaxLength="100" Nullable="false" />
          <Property Name="Email" Type="varchar" MaxLength="100" />
          <Property Name="NoTelp" Type="varchar" MaxLength="20" />
          <Property Name="Role_id" Type="int" Nullable="false" />
          <Property Name="IsActive" Type="bit" Nullable="false" />
          <Property Name="LastLogin" Type="datetime" />
          <Property Name="LastUpdate" Type="datetime" Nullable="false" />
        </EntityType>
        <EntityType Name="tr_jembatan_timbang">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="NoTransaksi" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="TanggalMasuk" Type="datetime" Nullable="false" />
          <Property Name="TanggalKeluar" Type="datetime" />
          <Property Name="Kendaraan_id" Type="int" Nullable="false" />
          <Property Name="NoPolisi" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaSupir" Type="nvarchar" MaxLength="100" />
          <Property Name="Mitra_id" Type="int" Nullable="false" />
          <Property Name="KodeMitra" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="NamaMitra" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="JenisMuatan" Type="nvarchar" MaxLength="100" />
          <Property Name="AsalMuatan" Type="nvarchar" MaxLength="100" />
          <Property Name="TujuanMuatan" Type="nvarchar" MaxLength="100" />
          <Property Name="BeratMasuk" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="BeratKeluar" Type="decimal" Precision="18" Scale="2" />
          <Property Name="BeratNetto" Type="decimal" Precision="18" Scale="2" />
          <Property Name="StatusTransaksi" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="NoBukti" Type="nvarchar" MaxLength="50" />
          <Property Name="Keterangan" Type="nvarchar(max)" />
          <Property Name="UserInput" Type="nvarchar" MaxLength="50" />
          <Property Name="UserUpdate" Type="nvarchar" MaxLength="50" />
          <Property Name="LastUpdate" Type="datetime" Nullable="false" />
          <Property Name="TotalBeratKemasan" Type="decimal" Precision="10" Scale="3" />
          <Property Name="BeratNettoMurni" Type="decimal" Precision="10" Scale="3" />
        </EntityType>
        <EntityType Name="tr_kemasan_detail">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="JembatanTimbang_id" Type="int" Nullable="false" />
          <Property Name="Kemasan_id" Type="int" Nullable="false" />
          <Property Name="Qty" Type="int" Nullable="false" />
          <Property Name="BeratSatuanKemasan" Type="decimal" Precision="10" Scale="3" Nullable="false" />
          <Property Name="TotalBeratKemasan" Type="decimal" Precision="21" Scale="3" StoreGeneratedPattern="Computed" />
          <Property Name="Keterangan" Type="nvarchar" MaxLength="255" />
          <Property Name="CreatedDate" Type="datetime" Nullable="false" />
          <Property Name="CreatedBy" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'D:\DATA\DXJBT2.MDF.dbo.vw_jembatan_timbang_with_kemasan' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="vw_jembatan_timbang_with_kemasan">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="NoTransaksi" />
            <PropertyRef Name="TanggalMasuk" />
            <PropertyRef Name="Kendaraan_id" />
            <PropertyRef Name="NoPolisi" />
            <PropertyRef Name="Mitra_id" />
            <PropertyRef Name="KodeMitra" />
            <PropertyRef Name="NamaMitra" />
            <PropertyRef Name="BeratMasuk" />
            <PropertyRef Name="StatusTransaksi" />
            <PropertyRef Name="LastUpdate" />
            <PropertyRef Name="TotalQtyKemasan" />
            <PropertyRef Name="JumlahJenisKemasan" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="NoTransaksi" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="TanggalMasuk" Type="datetime" Nullable="false" />
          <Property Name="TanggalKeluar" Type="datetime" />
          <Property Name="Kendaraan_id" Type="int" Nullable="false" />
          <Property Name="NoPolisi" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="NamaSupir" Type="nvarchar" MaxLength="100" />
          <Property Name="Mitra_id" Type="int" Nullable="false" />
          <Property Name="KodeMitra" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="NamaMitra" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="JenisMuatan" Type="nvarchar" MaxLength="100" />
          <Property Name="AsalMuatan" Type="nvarchar" MaxLength="100" />
          <Property Name="TujuanMuatan" Type="nvarchar" MaxLength="100" />
          <Property Name="BeratMasuk" Type="decimal" Precision="18" Scale="2" Nullable="false" />
          <Property Name="BeratKeluar" Type="decimal" Precision="18" Scale="2" />
          <Property Name="BeratNetto" Type="decimal" Precision="18" Scale="2" />
          <Property Name="StatusTransaksi" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="NoBukti" Type="nvarchar" MaxLength="50" />
          <Property Name="Keterangan" Type="nvarchar(max)" />
          <Property Name="UserInput" Type="nvarchar" MaxLength="50" />
          <Property Name="UserUpdate" Type="nvarchar" MaxLength="50" />
          <Property Name="LastUpdate" Type="datetime" Nullable="false" />
          <Property Name="TotalBeratKemasan" Type="decimal" Precision="10" Scale="3" />
          <Property Name="BeratNettoMurni" Type="decimal" Precision="10" Scale="3" />
          <Property Name="TotalQtyKemasan" Type="int" Nullable="false" />
          <Property Name="JumlahJenisKemasan" Type="int" Nullable="false" />
          <Property Name="BeratNettoMurniCalculated" Type="decimal" Precision="20" Scale="3" />
        </EntityType>
        <Association Name="FK_tm_kendaraan_Torm_mitra">
          <End Role="tm_mitra" Type="Self.tm_mitra" Multiplicity="1" />
          <End Role="tm_kendaraan" Type="Self.tm_kendaraan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_mitra">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_kendaraan">
              <PropertyRef Name="Mitra_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_kendaraan_Totm_tipe_kendaraan">
          <End Role="tm_tipe_kendaraan" Type="Self.tm_tipe_kendaraan" Multiplicity="1" />
          <End Role="tm_kendaraan" Type="Self.tm_kendaraan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_tipe_kendaraan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_kendaraan">
              <PropertyRef Name="TipeKendaraan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_user_tm_role">
          <End Role="tm_role" Type="Self.tm_role" Multiplicity="1" />
          <End Role="tm_user" Type="Self.tm_user" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user">
              <PropertyRef Name="Role_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_jembatan_timbang_tm_kendaraan">
          <End Role="tm_kendaraan" Type="Self.tm_kendaraan" Multiplicity="1" />
          <End Role="tr_jembatan_timbang" Type="Self.tr_jembatan_timbang" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_kendaraan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_jembatan_timbang">
              <PropertyRef Name="Kendaraan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_jembatan_timbang_tm_mitra">
          <End Role="tm_mitra" Type="Self.tm_mitra" Multiplicity="1" />
          <End Role="tr_jembatan_timbang" Type="Self.tr_jembatan_timbang" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_mitra">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_jembatan_timbang">
              <PropertyRef Name="Mitra_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kemasan_detail_JembatanTimbang">
          <End Role="tr_jembatan_timbang" Type="Self.tr_jembatan_timbang" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Role="tr_kemasan_detail" Type="Self.tr_kemasan_detail" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_jembatan_timbang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kemasan_detail">
              <PropertyRef Name="JembatanTimbang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kemasan_detail_Kemasan">
          <End Role="tm_kemasan" Type="Self.tm_kemasan" Multiplicity="1" />
          <End Role="tr_kemasan_detail" Type="Self.tr_kemasan_detail" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_kemasan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kemasan_detail">
              <PropertyRef Name="Kemasan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="dxjbt2ModelStoreContainer">
          <EntitySet Name="tm_kemasan" EntityType="Self.tm_kemasan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_kendaraan" EntityType="Self.tm_kendaraan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_mitra" EntityType="Self.tm_mitra" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_role" EntityType="Self.tm_role" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_settings" EntityType="Self.tm_settings" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_tipe_kendaraan" EntityType="Self.tm_tipe_kendaraan" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tm_user" EntityType="Self.tm_user" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_jembatan_timbang" EntityType="Self.tr_jembatan_timbang" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tr_kemasan_detail" EntityType="Self.tr_kemasan_detail" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="vw_jembatan_timbang_with_kemasan" EntityType="Self.vw_jembatan_timbang_with_kemasan" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [vw_jembatan_timbang_with_kemasan].[Id] AS [Id], 
    [vw_jembatan_timbang_with_kemasan].[NoTransaksi] AS [NoTransaksi], 
    [vw_jembatan_timbang_with_kemasan].[TanggalMasuk] AS [TanggalMasuk], 
    [vw_jembatan_timbang_with_kemasan].[TanggalKeluar] AS [TanggalKeluar], 
    [vw_jembatan_timbang_with_kemasan].[Kendaraan_id] AS [Kendaraan_id], 
    [vw_jembatan_timbang_with_kemasan].[NoPolisi] AS [NoPolisi], 
    [vw_jembatan_timbang_with_kemasan].[NamaSupir] AS [NamaSupir], 
    [vw_jembatan_timbang_with_kemasan].[Mitra_id] AS [Mitra_id], 
    [vw_jembatan_timbang_with_kemasan].[KodeMitra] AS [KodeMitra], 
    [vw_jembatan_timbang_with_kemasan].[NamaMitra] AS [NamaMitra], 
    [vw_jembatan_timbang_with_kemasan].[JenisMuatan] AS [JenisMuatan], 
    [vw_jembatan_timbang_with_kemasan].[AsalMuatan] AS [AsalMuatan], 
    [vw_jembatan_timbang_with_kemasan].[TujuanMuatan] AS [TujuanMuatan], 
    [vw_jembatan_timbang_with_kemasan].[BeratMasuk] AS [BeratMasuk], 
    [vw_jembatan_timbang_with_kemasan].[BeratKeluar] AS [BeratKeluar], 
    [vw_jembatan_timbang_with_kemasan].[BeratNetto] AS [BeratNetto], 
    [vw_jembatan_timbang_with_kemasan].[StatusTransaksi] AS [StatusTransaksi], 
    [vw_jembatan_timbang_with_kemasan].[NoBukti] AS [NoBukti], 
    [vw_jembatan_timbang_with_kemasan].[Keterangan] AS [Keterangan], 
    [vw_jembatan_timbang_with_kemasan].[UserInput] AS [UserInput], 
    [vw_jembatan_timbang_with_kemasan].[UserUpdate] AS [UserUpdate], 
    [vw_jembatan_timbang_with_kemasan].[LastUpdate] AS [LastUpdate], 
    [vw_jembatan_timbang_with_kemasan].[TotalBeratKemasan] AS [TotalBeratKemasan], 
    [vw_jembatan_timbang_with_kemasan].[BeratNettoMurni] AS [BeratNettoMurni], 
    [vw_jembatan_timbang_with_kemasan].[TotalQtyKemasan] AS [TotalQtyKemasan], 
    [vw_jembatan_timbang_with_kemasan].[JumlahJenisKemasan] AS [JumlahJenisKemasan], 
    [vw_jembatan_timbang_with_kemasan].[BeratNettoMurniCalculated] AS [BeratNettoMurniCalculated]
    FROM [dbo].[vw_jembatan_timbang_with_kemasan] AS [vw_jembatan_timbang_with_kemasan]</DefiningQuery>
          </EntitySet>
          <AssociationSet Name="FK_tm_kendaraan_Torm_mitra" Association="Self.FK_tm_kendaraan_Torm_mitra">
            <End Role="tm_mitra" EntitySet="tm_mitra" />
            <End Role="tm_kendaraan" EntitySet="tm_kendaraan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_kendaraan_Totm_tipe_kendaraan" Association="Self.FK_tm_kendaraan_Totm_tipe_kendaraan">
            <End Role="tm_tipe_kendaraan" EntitySet="tm_tipe_kendaraan" />
            <End Role="tm_kendaraan" EntitySet="tm_kendaraan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_user_tm_role" Association="Self.FK_tm_user_tm_role">
            <End Role="tm_role" EntitySet="tm_role" />
            <End Role="tm_user" EntitySet="tm_user" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_jembatan_timbang_tm_kendaraan" Association="Self.FK_tr_jembatan_timbang_tm_kendaraan">
            <End Role="tm_kendaraan" EntitySet="tm_kendaraan" />
            <End Role="tr_jembatan_timbang" EntitySet="tr_jembatan_timbang" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_jembatan_timbang_tm_mitra" Association="Self.FK_tr_jembatan_timbang_tm_mitra">
            <End Role="tm_mitra" EntitySet="tm_mitra" />
            <End Role="tr_jembatan_timbang" EntitySet="tr_jembatan_timbang" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kemasan_detail_JembatanTimbang" Association="Self.FK_tr_kemasan_detail_JembatanTimbang">
            <End Role="tr_jembatan_timbang" EntitySet="tr_jembatan_timbang" />
            <End Role="tr_kemasan_detail" EntitySet="tr_kemasan_detail" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kemasan_detail_Kemasan" Association="Self.FK_tr_kemasan_detail_Kemasan">
            <End Role="tm_kemasan" EntitySet="tm_kemasan" />
            <End Role="tr_kemasan_detail" EntitySet="tr_kemasan_detail" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="dxjbt2Model" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="tm_kendaraan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="NoPolisi" Type="String" MaxLength="10" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="TipeKendaraan_id" Type="Int32" Nullable="false" />
          <Property Name="Mitra_id" Type="Int32" Nullable="false" />
          <Property Name="NamaSupir" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <NavigationProperty Name="tm_mitra" Relationship="Self.FK_tm_kendaraan_Torm_mitra" FromRole="tm_kendaraan" ToRole="tm_mitra" />
          <NavigationProperty Name="tm_tipe_kendaraan" Relationship="Self.FK_tm_kendaraan_Totm_tipe_kendaraan" FromRole="tm_kendaraan" ToRole="tm_tipe_kendaraan" />
          <NavigationProperty Name="tr_jembatan_timbang" Relationship="dxjbt2Model.FK_tr_jembatan_timbang_tm_kendaraan" FromRole="tm_kendaraan" ToRole="tr_jembatan_timbang" />
        </EntityType>
        <EntityType Name="tm_mitra">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeMitra" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="NamaMitra" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Alamat" Type="String" MaxLength="250" FixedLength="false" Unicode="false" />
          <Property Name="Kota" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Telp" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tm_kendaraan" Relationship="Self.FK_tm_kendaraan_Torm_mitra" FromRole="tm_mitra" ToRole="tm_kendaraan" />
          <Property Name="TipeMitra" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tr_jembatan_timbang" Relationship="dxjbt2Model.FK_tr_jembatan_timbang_tm_mitra" FromRole="tm_mitra" ToRole="tr_jembatan_timbang" />
        </EntityType>
        <EntityType Name="tm_tipe_kendaraan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="NamaTipe" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="tm_kendaraan" Relationship="Self.FK_tm_kendaraan_Totm_tipe_kendaraan" FromRole="tm_tipe_kendaraan" ToRole="tm_kendaraan" />
        </EntityType>
        <Association Name="FK_tm_kendaraan_Torm_mitra">
          <End Role="tm_mitra" Type="Self.tm_mitra" Multiplicity="1" />
          <End Role="tm_kendaraan" Type="Self.tm_kendaraan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_mitra">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_kendaraan">
              <PropertyRef Name="Mitra_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tm_kendaraan_Totm_tipe_kendaraan">
          <End Role="tm_tipe_kendaraan" Type="Self.tm_tipe_kendaraan" Multiplicity="1" />
          <End Role="tm_kendaraan" Type="Self.tm_kendaraan" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_tipe_kendaraan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_kendaraan">
              <PropertyRef Name="TipeKendaraan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="dxjbt2Entities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="tm_kendaraan" EntityType="Self.tm_kendaraan" />
          <EntitySet Name="tm_mitra" EntityType="Self.tm_mitra" />
          <EntitySet Name="tm_tipe_kendaraan" EntityType="Self.tm_tipe_kendaraan" />
          <AssociationSet Name="FK_tm_kendaraan_Torm_mitra" Association="Self.FK_tm_kendaraan_Torm_mitra">
            <End Role="tm_mitra" EntitySet="tm_mitra" />
            <End Role="tm_kendaraan" EntitySet="tm_kendaraan" />
          </AssociationSet>
          <AssociationSet Name="FK_tm_kendaraan_Totm_tipe_kendaraan" Association="Self.FK_tm_kendaraan_Totm_tipe_kendaraan">
            <End Role="tm_tipe_kendaraan" EntitySet="tm_tipe_kendaraan" />
            <End Role="tm_kendaraan" EntitySet="tm_kendaraan" />
          </AssociationSet>
          <EntitySet Name="tr_jembatan_timbang" EntityType="dxjbt2Model.tr_jembatan_timbang" />
          <AssociationSet Name="FK_tr_jembatan_timbang_tm_kendaraan" Association="dxjbt2Model.FK_tr_jembatan_timbang_tm_kendaraan">
            <End Role="tm_kendaraan" EntitySet="tm_kendaraan" />
            <End Role="tr_jembatan_timbang" EntitySet="tr_jembatan_timbang" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_jembatan_timbang_tm_mitra" Association="dxjbt2Model.FK_tr_jembatan_timbang_tm_mitra">
            <End Role="tm_mitra" EntitySet="tm_mitra" />
            <End Role="tr_jembatan_timbang" EntitySet="tr_jembatan_timbang" />
          </AssociationSet>
          <EntitySet Name="tm_settings" EntityType="dxjbt2Model.tm_settings" />
          <EntitySet Name="tm_role" EntityType="dxjbt2Model.tm_role" />
          <EntitySet Name="tm_user" EntityType="dxjbt2Model.tm_user" />
          <AssociationSet Name="FK_tm_user_tm_role" Association="dxjbt2Model.FK_tm_user_tm_role">
            <End Role="tm_role" EntitySet="tm_role" />
            <End Role="tm_user" EntitySet="tm_user" />
          </AssociationSet>
          <EntitySet Name="tm_kemasan" EntityType="dxjbt2Model.tm_kemasan" />
          <EntitySet Name="tr_kemasan_detail" EntityType="dxjbt2Model.tr_kemasan_detail" />
          <AssociationSet Name="FK_tr_kemasan_detail_Kemasan" Association="dxjbt2Model.FK_tr_kemasan_detail_Kemasan">
            <End Role="tm_kemasan" EntitySet="tm_kemasan" />
            <End Role="tr_kemasan_detail" EntitySet="tr_kemasan_detail" />
          </AssociationSet>
          <AssociationSet Name="FK_tr_kemasan_detail_JembatanTimbang" Association="dxjbt2Model.FK_tr_kemasan_detail_JembatanTimbang">
            <End Role="tr_jembatan_timbang" EntitySet="tr_jembatan_timbang" />
            <End Role="tr_kemasan_detail" EntitySet="tr_kemasan_detail" />
          </AssociationSet>
          <EntitySet Name="vw_jembatan_timbang_with_kemasan" EntityType="dxjbt2Model.vw_jembatan_timbang_with_kemasan" />
        </EntityContainer>
        <EntityType Name="tr_jembatan_timbang">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="NoTransaksi" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="TanggalMasuk" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="TanggalKeluar" Type="DateTime" Precision="3" />
          <Property Name="Kendaraan_id" Type="Int32" Nullable="false" />
          <Property Name="NoPolisi" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="NamaSupir" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Mitra_id" Type="Int32" Nullable="false" />
          <Property Name="KodeMitra" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="NamaMitra" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="JenisMuatan" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="AsalMuatan" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="TujuanMuatan" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="BeratMasuk" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="BeratKeluar" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="BeratNetto" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="StatusTransaksi" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="NoBukti" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Keterangan" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="UserInput" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="UserUpdate" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastUpdate" Type="DateTime" Nullable="false" Precision="3" />
          <NavigationProperty Name="tm_kendaraan" Relationship="dxjbt2Model.FK_tr_jembatan_timbang_tm_kendaraan" FromRole="tr_jembatan_timbang" ToRole="tm_kendaraan" />
          <NavigationProperty Name="tm_mitra" Relationship="dxjbt2Model.FK_tr_jembatan_timbang_tm_mitra" FromRole="tr_jembatan_timbang" ToRole="tm_mitra" />
          <NavigationProperty Name="tr_kemasan_detail" Relationship="dxjbt2Model.FK_tr_kemasan_detail_JembatanTimbang" FromRole="tr_jembatan_timbang" ToRole="tr_kemasan_detail" />
          <Property Name="TotalBeratKemasan" Type="Decimal" Precision="10" Scale="3" />
          <Property Name="BeratNettoMurni" Type="Decimal" Precision="10" Scale="3" />
        </EntityType>
        <Association Name="FK_tr_jembatan_timbang_tm_kendaraan">
          <End Type="dxjbt2Model.tm_kendaraan" Role="tm_kendaraan" Multiplicity="1" />
          <End Type="dxjbt2Model.tr_jembatan_timbang" Role="tr_jembatan_timbang" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_kendaraan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_jembatan_timbang">
              <PropertyRef Name="Kendaraan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_jembatan_timbang_tm_mitra">
          <End Type="dxjbt2Model.tm_mitra" Role="tm_mitra" Multiplicity="1" />
          <End Type="dxjbt2Model.tr_jembatan_timbang" Role="tr_jembatan_timbang" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_mitra">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_jembatan_timbang">
              <PropertyRef Name="Mitra_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_settings">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SettingName" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="SettingValue" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="Description" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="LastUpdate" Type="DateTime" Nullable="false" Precision="3" />
        </EntityType>
        <EntityType Name="tm_role">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeRole" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="NamaRole" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Deskripsi" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="LastUpdate" Type="DateTime" Nullable="false" Precision="3" />
          <NavigationProperty Name="tm_user" Relationship="dxjbt2Model.FK_tm_user_tm_role" FromRole="tm_role" ToRole="tm_user" />
        </EntityType>
        <EntityType Name="tm_user">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Username" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="Password" Type="String" Nullable="false" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="NamaLengkap" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="Email" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="NoTelp" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="Role_id" Type="Int32" Nullable="false" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="LastLogin" Type="DateTime" Precision="3" />
          <Property Name="LastUpdate" Type="DateTime" Nullable="false" Precision="3" />
          <NavigationProperty Name="tm_role" Relationship="dxjbt2Model.FK_tm_user_tm_role" FromRole="tm_user" ToRole="tm_role" />
        </EntityType>
        <Association Name="FK_tm_user_tm_role">
          <End Type="dxjbt2Model.tm_role" Role="tm_role" Multiplicity="1" />
          <End Type="dxjbt2Model.tm_user" Role="tm_user" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_role">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tm_user">
              <PropertyRef Name="Role_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="tm_kemasan">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="KodeKemasan" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="NamaKemasan" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="UkuranKemasan" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="BeratKemasan" Type="Decimal" Nullable="false" Precision="10" Scale="3" />
          <Property Name="Satuan" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Keterangan" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="IsActive" Type="Boolean" Nullable="false" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="tr_kemasan_detail" Relationship="dxjbt2Model.FK_tr_kemasan_detail_Kemasan" FromRole="tm_kemasan" ToRole="tr_kemasan_detail" />
        </EntityType>
        <EntityType Name="tr_kemasan_detail">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="JembatanTimbang_id" Type="Int32" Nullable="false" />
          <Property Name="Kemasan_id" Type="Int32" Nullable="false" />
          <Property Name="Qty" Type="Int32" Nullable="false" />
          <Property Name="BeratSatuanKemasan" Type="Decimal" Nullable="false" Precision="10" Scale="3" />
          <Property Name="TotalBeratKemasan" Type="Decimal" Precision="21" Scale="3" annotation:StoreGeneratedPattern="Computed" />
          <Property Name="Keterangan" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="CreatedBy" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="tm_kemasan" Relationship="dxjbt2Model.FK_tr_kemasan_detail_Kemasan" FromRole="tr_kemasan_detail" ToRole="tm_kemasan" />
          <NavigationProperty Name="tr_jembatan_timbang" Relationship="dxjbt2Model.FK_tr_kemasan_detail_JembatanTimbang" FromRole="tr_kemasan_detail" ToRole="tr_jembatan_timbang" />
        </EntityType>
        <Association Name="FK_tr_kemasan_detail_Kemasan">
          <End Type="dxjbt2Model.tm_kemasan" Role="tm_kemasan" Multiplicity="1" />
          <End Type="dxjbt2Model.tr_kemasan_detail" Role="tr_kemasan_detail" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tm_kemasan">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kemasan_detail">
              <PropertyRef Name="Kemasan_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tr_kemasan_detail_JembatanTimbang">
          <End Type="dxjbt2Model.tr_jembatan_timbang" Role="tr_jembatan_timbang" Multiplicity="1">
            <OnDelete Action="Cascade" />
          </End>
          <End Type="dxjbt2Model.tr_kemasan_detail" Role="tr_kemasan_detail" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="tr_jembatan_timbang">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="tr_kemasan_detail">
              <PropertyRef Name="JembatanTimbang_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="vw_jembatan_timbang_with_kemasan">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="NoTransaksi" />
            <PropertyRef Name="TanggalMasuk" />
            <PropertyRef Name="Kendaraan_id" />
            <PropertyRef Name="NoPolisi" />
            <PropertyRef Name="Mitra_id" />
            <PropertyRef Name="KodeMitra" />
            <PropertyRef Name="NamaMitra" />
            <PropertyRef Name="BeratMasuk" />
            <PropertyRef Name="StatusTransaksi" />
            <PropertyRef Name="LastUpdate" />
            <PropertyRef Name="TotalQtyKemasan" />
            <PropertyRef Name="JumlahJenisKemasan" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="NoTransaksi" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="TanggalMasuk" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="TanggalKeluar" Type="DateTime" Precision="3" />
          <Property Name="Kendaraan_id" Type="Int32" Nullable="false" />
          <Property Name="NoPolisi" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="NamaSupir" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Mitra_id" Type="Int32" Nullable="false" />
          <Property Name="KodeMitra" Type="String" Nullable="false" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="NamaMitra" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="JenisMuatan" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="AsalMuatan" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="TujuanMuatan" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="BeratMasuk" Type="Decimal" Nullable="false" Precision="18" Scale="2" />
          <Property Name="BeratKeluar" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="BeratNetto" Type="Decimal" Precision="18" Scale="2" />
          <Property Name="StatusTransaksi" Type="String" Nullable="false" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="NoBukti" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Keterangan" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="UserInput" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="UserUpdate" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastUpdate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="TotalBeratKemasan" Type="Decimal" Precision="10" Scale="3" />
          <Property Name="BeratNettoMurni" Type="Decimal" Precision="10" Scale="3" />
          <Property Name="TotalQtyKemasan" Type="Int32" Nullable="false" />
          <Property Name="JumlahJenisKemasan" Type="Int32" Nullable="false" />
          <Property Name="BeratNettoMurniCalculated" Type="Decimal" Precision="20" Scale="3" />
        </EntityType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="dxjbt2ModelStoreContainer" CdmEntityContainer="dxjbt2Entities">
          <EntitySetMapping Name="tm_kendaraan">
            <EntityTypeMapping TypeName="dxjbt2Model.tm_kendaraan">
              <MappingFragment StoreEntitySet="tm_kendaraan">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NoPolisi" ColumnName="NoPolisi" />
                <ScalarProperty Name="TipeKendaraan_id" ColumnName="TipeKendaraan_id" />
                <ScalarProperty Name="Mitra_id" ColumnName="Mitra_id" />
                <ScalarProperty Name="NamaSupir" ColumnName="NamaSupir" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_mitra">
            <EntityTypeMapping TypeName="dxjbt2Model.tm_mitra">
              <MappingFragment StoreEntitySet="tm_mitra">
                <ScalarProperty Name="TipeMitra" ColumnName="TipeMitra" />
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="KodeMitra" ColumnName="KodeMitra" />
                <ScalarProperty Name="NamaMitra" ColumnName="NamaMitra" />
                <ScalarProperty Name="Alamat" ColumnName="Alamat" />
                <ScalarProperty Name="Kota" ColumnName="Kota" />
                <ScalarProperty Name="Telp" ColumnName="Telp" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_tipe_kendaraan">
            <EntityTypeMapping TypeName="dxjbt2Model.tm_tipe_kendaraan">
              <MappingFragment StoreEntitySet="tm_tipe_kendaraan">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="NamaTipe" ColumnName="NamaTipe" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_jembatan_timbang">
            <EntityTypeMapping TypeName="dxjbt2Model.tr_jembatan_timbang">
              <MappingFragment StoreEntitySet="tr_jembatan_timbang">
                <ScalarProperty Name="BeratNettoMurni" ColumnName="BeratNettoMurni" />
                <ScalarProperty Name="TotalBeratKemasan" ColumnName="TotalBeratKemasan" />
                <ScalarProperty Name="LastUpdate" ColumnName="LastUpdate" />
                <ScalarProperty Name="UserUpdate" ColumnName="UserUpdate" />
                <ScalarProperty Name="UserInput" ColumnName="UserInput" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="NoBukti" ColumnName="NoBukti" />
                <ScalarProperty Name="StatusTransaksi" ColumnName="StatusTransaksi" />
                <ScalarProperty Name="BeratNetto" ColumnName="BeratNetto" />
                <ScalarProperty Name="BeratKeluar" ColumnName="BeratKeluar" />
                <ScalarProperty Name="BeratMasuk" ColumnName="BeratMasuk" />
                <ScalarProperty Name="TujuanMuatan" ColumnName="TujuanMuatan" />
                <ScalarProperty Name="AsalMuatan" ColumnName="AsalMuatan" />
                <ScalarProperty Name="JenisMuatan" ColumnName="JenisMuatan" />
                <ScalarProperty Name="NamaMitra" ColumnName="NamaMitra" />
                <ScalarProperty Name="KodeMitra" ColumnName="KodeMitra" />
                <ScalarProperty Name="Mitra_id" ColumnName="Mitra_id" />
                <ScalarProperty Name="NamaSupir" ColumnName="NamaSupir" />
                <ScalarProperty Name="NoPolisi" ColumnName="NoPolisi" />
                <ScalarProperty Name="Kendaraan_id" ColumnName="Kendaraan_id" />
                <ScalarProperty Name="TanggalKeluar" ColumnName="TanggalKeluar" />
                <ScalarProperty Name="TanggalMasuk" ColumnName="TanggalMasuk" />
                <ScalarProperty Name="NoTransaksi" ColumnName="NoTransaksi" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_settings">
            <EntityTypeMapping TypeName="dxjbt2Model.tm_settings">
              <MappingFragment StoreEntitySet="tm_settings">
                <ScalarProperty Name="LastUpdate" ColumnName="LastUpdate" />
                <ScalarProperty Name="Description" ColumnName="Description" />
                <ScalarProperty Name="SettingValue" ColumnName="SettingValue" />
                <ScalarProperty Name="SettingName" ColumnName="SettingName" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_role">
            <EntityTypeMapping TypeName="dxjbt2Model.tm_role">
              <MappingFragment StoreEntitySet="tm_role">
                <ScalarProperty Name="LastUpdate" ColumnName="LastUpdate" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="Deskripsi" ColumnName="Deskripsi" />
                <ScalarProperty Name="NamaRole" ColumnName="NamaRole" />
                <ScalarProperty Name="KodeRole" ColumnName="KodeRole" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_user">
            <EntityTypeMapping TypeName="dxjbt2Model.tm_user">
              <MappingFragment StoreEntitySet="tm_user">
                <ScalarProperty Name="LastUpdate" ColumnName="LastUpdate" />
                <ScalarProperty Name="LastLogin" ColumnName="LastLogin" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="Role_id" ColumnName="Role_id" />
                <ScalarProperty Name="NoTelp" ColumnName="NoTelp" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="NamaLengkap" ColumnName="NamaLengkap" />
                <ScalarProperty Name="Password" ColumnName="Password" />
                <ScalarProperty Name="Username" ColumnName="Username" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tm_kemasan">
            <EntityTypeMapping TypeName="dxjbt2Model.tm_kemasan">
              <MappingFragment StoreEntitySet="tm_kemasan">
                <ScalarProperty Name="ModifiedBy" ColumnName="ModifiedBy" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="IsActive" ColumnName="IsActive" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="Satuan" ColumnName="Satuan" />
                <ScalarProperty Name="BeratKemasan" ColumnName="BeratKemasan" />
                <ScalarProperty Name="UkuranKemasan" ColumnName="UkuranKemasan" />
                <ScalarProperty Name="NamaKemasan" ColumnName="NamaKemasan" />
                <ScalarProperty Name="KodeKemasan" ColumnName="KodeKemasan" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tr_kemasan_detail">
            <EntityTypeMapping TypeName="dxjbt2Model.tr_kemasan_detail">
              <MappingFragment StoreEntitySet="tr_kemasan_detail">
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="TotalBeratKemasan" ColumnName="TotalBeratKemasan" />
                <ScalarProperty Name="BeratSatuanKemasan" ColumnName="BeratSatuanKemasan" />
                <ScalarProperty Name="Qty" ColumnName="Qty" />
                <ScalarProperty Name="Kemasan_id" ColumnName="Kemasan_id" />
                <ScalarProperty Name="JembatanTimbang_id" ColumnName="JembatanTimbang_id" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="vw_jembatan_timbang_with_kemasan">
            <EntityTypeMapping TypeName="dxjbt2Model.vw_jembatan_timbang_with_kemasan">
              <MappingFragment StoreEntitySet="vw_jembatan_timbang_with_kemasan">
                <ScalarProperty Name="BeratNettoMurniCalculated" ColumnName="BeratNettoMurniCalculated" />
                <ScalarProperty Name="JumlahJenisKemasan" ColumnName="JumlahJenisKemasan" />
                <ScalarProperty Name="TotalQtyKemasan" ColumnName="TotalQtyKemasan" />
                <ScalarProperty Name="BeratNettoMurni" ColumnName="BeratNettoMurni" />
                <ScalarProperty Name="TotalBeratKemasan" ColumnName="TotalBeratKemasan" />
                <ScalarProperty Name="LastUpdate" ColumnName="LastUpdate" />
                <ScalarProperty Name="UserUpdate" ColumnName="UserUpdate" />
                <ScalarProperty Name="UserInput" ColumnName="UserInput" />
                <ScalarProperty Name="Keterangan" ColumnName="Keterangan" />
                <ScalarProperty Name="NoBukti" ColumnName="NoBukti" />
                <ScalarProperty Name="StatusTransaksi" ColumnName="StatusTransaksi" />
                <ScalarProperty Name="BeratNetto" ColumnName="BeratNetto" />
                <ScalarProperty Name="BeratKeluar" ColumnName="BeratKeluar" />
                <ScalarProperty Name="BeratMasuk" ColumnName="BeratMasuk" />
                <ScalarProperty Name="TujuanMuatan" ColumnName="TujuanMuatan" />
                <ScalarProperty Name="AsalMuatan" ColumnName="AsalMuatan" />
                <ScalarProperty Name="JenisMuatan" ColumnName="JenisMuatan" />
                <ScalarProperty Name="NamaMitra" ColumnName="NamaMitra" />
                <ScalarProperty Name="KodeMitra" ColumnName="KodeMitra" />
                <ScalarProperty Name="Mitra_id" ColumnName="Mitra_id" />
                <ScalarProperty Name="NamaSupir" ColumnName="NamaSupir" />
                <ScalarProperty Name="NoPolisi" ColumnName="NoPolisi" />
                <ScalarProperty Name="Kendaraan_id" ColumnName="Kendaraan_id" />
                <ScalarProperty Name="TanggalKeluar" ColumnName="TanggalKeluar" />
                <ScalarProperty Name="TanggalMasuk" ColumnName="TanggalMasuk" />
                <ScalarProperty Name="NoTransaksi" ColumnName="NoTransaksi" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>