-- <PERSON>ript untuk insert data menu dan sample role access
-- Jalankan script ini setelah tabel tm_menu dan tm_role_menu_access sudah dibuat

-- Insert data menu utama (parent menus)
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive, CreatedDate, CreatedBy) VALUES
-- Main Menu Groups
('MASTER', 'Master Data', NULL, NULL, 'folder', 1, 1, GETDATE(), 'System'),
('TRANSAKSI', 'Transaksi', NULL, NULL, 'folder', 2, 1, GETDATE(), 'System'),
('LAPORAN', 'Laporan', NULL, NULL, 'folder', 3, 1, GETDATE(), 'System'),
('SECURITY', 'Security', NULL, NULL, 'folder', 4, 1, GETDATE(), 'System'),
('SETTING', 'Setting', NULL, NULL, 'folder', 5, 1, GETDATE(), 'System');

-- Dapatkan ID dari parent menus untuk referensi
DECLARE @MasterID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'MASTER');
DECLARE @TransaksiID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'TRANSAKSI');
DECLARE @LaporanID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'LAPORAN');
DECLARE @SecurityID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'SECURITY');
DECLARE @SettingID INT = (SELECT Id FROM tm_menu WHERE KodeMenu = 'SETTING');

-- Insert sub menus untuk Master Data
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive, CreatedDate, CreatedBy) VALUES
('MASTER_KEMASAN', 'Master Kemasan', @MasterID, 'frmKemasan', 'box', 101, 1, GETDATE(), 'System'),
('MASTER_KENDARAAN', 'Master Kendaraan', @MasterID, 'frmKendaraan', 'truck', 102, 1, GETDATE(), 'System'),
('MASTER_MITRA', 'Master Mitra', @MasterID, 'frmMitra', 'users', 103, 1, GETDATE(), 'System'),
('MASTER_TIPE_KENDARAAN', 'Master Tipe Kendaraan', @MasterID, 'frmTipeKendaraan', 'car', 104, 1, GETDATE(), 'System');

-- Insert sub menus untuk Transaksi
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive, CreatedDate, CreatedBy) VALUES
('TRANS_JEMBATAN_TIMBANG', 'Jembatan Timbang', @TransaksiID, 'frmJembatanTimbang', 'scale', 201, 1, GETDATE(), 'System'),
('TRANS_KEMASAN_DETAIL', 'Detail Kemasan', @TransaksiID, 'frmKemasanDetail', 'package', 202, 1, GETDATE(), 'System');

-- Insert sub menus untuk Laporan
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive, CreatedDate, CreatedBy) VALUES
('LAP_TRANSAKSI', 'Laporan Transaksi', @LaporanID, 'frmLaporanTransaksi', 'report', 301, 1, GETDATE(), 'System'),
('LAP_KEMASAN', 'Laporan Kemasan', @LaporanID, 'frmLaporanKemasan', 'chart', 302, 1, GETDATE(), 'System'),
('LAP_SUMMARY', 'Laporan Summary', @LaporanID, 'frmLaporanSummary', 'chart-bar', 303, 1, GETDATE(), 'System');

-- Insert sub menus untuk Security
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive, CreatedDate, CreatedBy) VALUES
('SEC_USER', 'Manajemen User', @SecurityID, 'frmUser', 'user', 401, 1, GETDATE(), 'System'),
('SEC_ROLE', 'Manajemen Role', @SecurityID, 'frmRole', 'shield', 402, 1, GETDATE(), 'System'),
('SEC_ROLE_ACCESS', 'Role Menu Access', @SecurityID, 'frmRoleMenuAccess', 'key', 403, 1, GETDATE(), 'System');

-- Insert sub menus untuk Setting
INSERT INTO tm_menu (KodeMenu, NamaMenu, ParentMenu_id, FormName, MenuIcon, MenuOrder, IsActive, CreatedDate, CreatedBy) VALUES
('SET_GENERAL', 'General Setting', @SettingID, 'frmSettings', 'settings', 501, 1, GETDATE(), 'System'),
('SET_PRINTER', 'Printer Setting', @SettingID, 'frmPrinterSettings', 'printer', 502, 1, GETDATE(), 'System'),
('SET_BACKUP', 'Backup & Restore', @SettingID, 'frmBackupRestore', 'database', 503, 1, GETDATE(), 'System');

-- Sample data untuk role access (asumsi Role ID: 1 = Administrator, 2 = Operator, 3 = Viewer)
-- Administrator tidak perlu data karena akan di-handle di kode (full access)

-- Role Operator (Role_id = 2) - akses terbatas
-- Master Data (view only untuk kemasan dan kendaraan)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 2, Id, 1, 0, 0, 0, 1, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu IN ('MASTER_KEMASAN', 'MASTER_KENDARAAN', 'MASTER_MITRA');

-- Transaksi (full access untuk jembatan timbang, limited untuk kemasan detail)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 2, Id, 1, 1, 1, 0, 1, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu = 'TRANS_JEMBATAN_TIMBANG';

INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 2, Id, 1, 1, 1, 1, 1, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu = 'TRANS_KEMASAN_DETAIL';

-- Laporan (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 2, Id, 1, 0, 0, 0, 1, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu IN ('LAP_TRANSAKSI', 'LAP_KEMASAN');

-- Role Viewer (Role_id = 3) - hanya bisa melihat dan print
-- Master Data (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 3, Id, 1, 0, 0, 0, 1, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu IN ('MASTER_KEMASAN', 'MASTER_KENDARAAN', 'MASTER_MITRA');

-- Transaksi (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 3, Id, 1, 0, 0, 0, 1, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu IN ('TRANS_JEMBATAN_TIMBANG', 'TRANS_KEMASAN_DETAIL');

-- Laporan (view & print only)
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 3, Id, 1, 0, 0, 0, 1, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu IN ('LAP_TRANSAKSI', 'LAP_KEMASAN', 'LAP_SUMMARY');

-- Tambahkan akses untuk parent menu (agar bisa terlihat)
-- Operator - akses ke parent menu
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 2, Id, 1, 0, 0, 0, 0, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu IN ('MASTER', 'TRANSAKSI', 'LAPORAN');

-- Viewer - akses ke parent menu
INSERT INTO tm_role_menu_access (Role_id, Menu_id, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CreatedDate, CreatedBy) 
SELECT 3, Id, 1, 0, 0, 0, 0, GETDATE(), 'System' 
FROM tm_menu WHERE KodeMenu IN ('MASTER', 'TRANSAKSI', 'LAPORAN');

-- Verifikasi data yang sudah diinsert
SELECT 'Menu Data' as TableName, COUNT(*) as RecordCount FROM tm_menu WHERE IsActive = 1
UNION ALL
SELECT 'Role Menu Access Data' as TableName, COUNT(*) as RecordCount FROM tm_role_menu_access;

-- Tampilkan struktur menu
SELECT 
    CASE WHEN m.ParentMenu_id IS NULL THEN m.NamaMenu 
         ELSE '  └─ ' + m.NamaMenu END as MenuStructure,
    m.KodeMenu,
    m.FormName,
    m.MenuOrder
FROM tm_menu m
WHERE m.IsActive = 1
ORDER BY 
    ISNULL(m.ParentMenu_id, m.Id), 
    m.MenuOrder;

-- Tampilkan sample role access
SELECT 
    r.NamaRole,
    m.NamaMenu,
    rma.CanView,
    rma.CanAdd,
    rma.CanEdit,
    rma.CanDelete,
    rma.CanPrint
FROM tm_role_menu_access rma
INNER JOIN tm_role r ON rma.Role_id = r.Id
INNER JOIN tm_menu m ON rma.Menu_id = m.Id
WHERE r.IsActive = 1 AND m.IsActive = 1
ORDER BY r.NamaRole, m.MenuOrder;
