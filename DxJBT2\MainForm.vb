Imports DxJBT2.SettingsHelper
Imports DxJBT2.Models
Imports DxJBT2.Forms.Security
Imports DxJBT2.Helpers

Public Class MainForm
    Public Delegate Sub MsgHandler(ByVal msg As String)
    Private CurrForm As DevExpress.XtraEditors.XtraForm
    Private modDbAction As MsgHandler
    
    ' Deklarasi variabel menu item dan komponen UI
    Friend WithEvents LogoutToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents StatusStrip1 As System.Windows.Forms.StatusStrip
    Friend WithEvents lblUserInfo As System.Windows.Forms.ToolStripStatusLabel
    Private Sub ShowManage(ByVal f As DevExpress.XtraEditors.XtraForm)
        CurrForm = f
        f.Show()
        f.WindowState = FormWindowState.Maximized
        ShowToolbars(New Boolean() {True, True, True, True, True, True})
    End Sub
    Private Sub showTipeKendaraan()
        Dim f As frmTipeKendaraan = frmTipeKendaraan.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub
    Private Sub showMitra()
        Dim f As frmMitra = frmMitra.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub

    Private Sub showKemasan()
        Dim f As frmKemasan = frmKemasan.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub

    Private Sub showKendaraan()
        Dim f As frmKendaraan = frmKendaraan.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub
    Private Sub TipeKendaraanToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles TipeKendaraanToolStripMenuItem.Click
        showTipeKendaraan()
    End Sub

    Private Sub tbNew_Click(sender As Object, e As EventArgs) Handles tbAddNew.Click
        modDbAction("Add")
    End Sub
    Private Sub ShowToolbars(ByVal tb As Boolean())
        tbAddNew.Visible = tb(0)
        tbEdit.Visible = tb(1)
        tbView.Visible = tb(2)
        tbDelete.Visible = tb(3)
        tbSeparatorClose.Visible = tb(4)
        tbClose.Visible = tb(5)
    End Sub

    Private Sub MainForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' Initialize default settings
        SettingsHelper.InitializeDefaultSettings()

        ' Hide toolbars initially
        ShowToolbars(New Boolean() {False, False, False, False, False, False})
        
        ' Tampilkan informasi user yang sedang login
        UpdateUserInfo()
        
        ' Set hak akses menu berdasarkan role user
        SetMenuAccess()
    End Sub

    Private Sub tbClose_Click(sender As Object, e As EventArgs) Handles tbClose.Click
        modDbAction("exit")
        ShowToolbars(New Boolean() {False, False, False, False, False, False})
        CurrForm = Nothing
    End Sub

    Private Sub tbEdit_Click(sender As Object, e As EventArgs) Handles tbEdit.Click
        modDbAction("Edit")
    End Sub

    Private Sub tbView_Click(sender As Object, e As EventArgs) Handles tbView.Click
        modDbAction("View")
    End Sub

    Private Sub tbDelete_Click(sender As Object, e As EventArgs) Handles tbDelete.Click
        modDbAction("Delete")
    End Sub

    Private Sub MitraToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles MitraToolStripMenuItem.Click
        showMitra()
    End Sub

    Private Sub KemasanToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles KemasanToolStripMenuItem.Click
        showKemasan()
    End Sub

    Private Sub KendaraanToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles KendaraanToolStripMenuItem.Click
        showKendaraan()
    End Sub

    Private Sub PenimbanganToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles PenimbanganToolStripMenuItem.Click
        showJembatanTimbang()
    End Sub

    Private Sub showJembatanTimbang()
        Dim f As frmJembatanTimbang = frmJembatanTimbang.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub

    Private Sub showSettings()
        ' Create and show settings form
        Dim f As New frmSettings()
        f.ShowDialog()
    End Sub

    Private Sub SettingsToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles SettingsToolStripMenuItem.Click
        showSettings()
    End Sub

    Private Sub showRole()
        Dim f As New frmRole()
        f.MdiParent = Me
        ShowManage(f)
    End Sub

    Private Sub showUser()
        Dim f As New frmUser()
        f.MdiParent = Me
        ShowManage(f)
    End Sub

    Private Sub RoleToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles RoleToolStripMenuItem.Click
        showRole()
        ShowToolbars(New Boolean() {False, False, False, False, False, False})
    End Sub

    Private Sub UserToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles UserToolStripMenuItem.Click
        showUser()
        ShowToolbars(New Boolean() {False, False, False, False, False, False})
    End Sub

    Private Sub SecurityToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles SecurityToolStripMenuItem.Click
        ' Tidak perlu melakukan apa-apa karena ini hanya menu dropdown
    End Sub

    ' Fungsi untuk logout
    Private Sub LogoutToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles LogoutToolStripMenuItem.Click
        If MessageBox.Show("Apakah Anda yakin ingin logout?", "Konfirmasi Logout", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            ' Logout user
            SecurityHelper.Logout()
            
            ' Tutup semua form
            For Each f As Form In Me.MdiChildren
                f.Close()
            Next
            
            ' Restart aplikasi untuk menampilkan form login
            Application.Restart()
        End If
    End Sub

    ' MainForm_Load sudah digabungkan dengan metode di atas

    Private Sub UpdateUserInfo()
        ' Tampilkan username di status bar
        If SecurityHelper.IsLoggedIn() Then
            lblUserInfo.Text = $"User: {SecurityHelper.GetCurrentUsername()}"
        Else
            lblUserInfo.Text = "User: Not logged in"
        End If
    End Sub

    Private Sub SetMenuAccess()
        ' Set akses menu berdasarkan role user menggunakan sistem database
        Try
            ' Build menu dinamis berdasarkan database
            MenuHelper.BuildMenuStrip(MenuStrip1)

            ' Set akses untuk menu yang masih hardcoded (jika ada)
            ' Menu User Management
            If UserToolStripMenuItem IsNot Nothing Then
                UserToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("SEC_USER", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' Menu Role Management
            If RoleToolStripMenuItem IsNot Nothing Then
                RoleToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("SEC_ROLE", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' Menu Transaksi/Penimbangan
            If ToolStripButton1 IsNot Nothing Then
                ToolStripButton1.Visible = SecurityHelper.HasMenuAccess("TRANS_JEMBATAN_TIMBANG", SecurityHelper.AccessRight.ViewOnly)
            End If

            If PenimbanganToolStripMenuItem IsNot Nothing Then
                PenimbanganToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("TRANS_JEMBATAN_TIMBANG", SecurityHelper.AccessRight.ViewOnly)
            End If
        
            ' Menu Master Data
            If ToolStripDropDownButton2 IsNot Nothing Then
                ToolStripDropDownButton2.Visible = SecurityHelper.HasMenuAccess("MASTER", SecurityHelper.AccessRight.ViewOnly)
            End If

        Catch ex As Exception
            MessageBox.Show($"Error setting menu access: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class