Imports DxJBT2.SettingsHelper
Imports DxJBT2.Models
Imports DxJBT2.Forms.Security
Imports DxJBT2.Helpers

Public Class MainForm
    Public Delegate Sub MsgHandler(ByVal msg As String)
    Private CurrForm As DevExpress.XtraEditors.XtraForm
    Private modDbAction As MsgHandler
    
    ' Deklarasi variabel menu item dan komponen UI
    Friend WithEvents LogoutToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents StatusStrip1 As System.Windows.Forms.StatusStrip
    Friend WithEvents lblUserInfo As System.Windows.Forms.ToolStripStatusLabel
    Private Sub ShowManage(ByVal f As DevExpress.XtraEditors.XtraForm)
        CurrForm = f
        f.Show()
        f.WindowState = FormWindowState.Maximized
        ShowToolbars(New Boolean() {True, True, True, True, True, True})
    End Sub
    Private Sub showTipeKendaraan()
        Dim f As frmTipeKendaraan = frmTipeKendaraan.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub
    Private Sub showMitra()
        Dim f As frmMitra = frmMitra.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub

    Private Sub showKemasan()
        Dim f As frmKemasan = frmKemasan.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub

    Private Sub showKendaraan()
        Dim f As frmKendaraan = frmKendaraan.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub

    Private Sub tbNew_Click(sender As Object, e As EventArgs) Handles tbAddNew.Click
        modDbAction("Add")
    End Sub
    Private Sub ShowToolbars(ByVal tb As Boolean())
        tbAddNew.Visible = tb(0)
        tbEdit.Visible = tb(1)
        tbView.Visible = tb(2)
        tbDelete.Visible = tb(3)
        tbSeparatorClose.Visible = tb(4)
        tbClose.Visible = tb(5)
    End Sub

    Private Sub MainForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        ' Initialize default settings
        SettingsHelper.InitializeDefaultSettings()

        ' Hide toolbars initially
        ShowToolbars(New Boolean() {False, False, False, False, False, False})
        
        ' Tampilkan informasi user yang sedang login
        UpdateUserInfo()
        
        ' Set hak akses menu berdasarkan role user
        SetMenuAccess()
    End Sub

    Private Sub tbClose_Click(sender As Object, e As EventArgs) Handles tbClose.Click
        modDbAction("exit")
        ShowToolbars(New Boolean() {False, False, False, False, False, False})
        CurrForm = Nothing
    End Sub

    Private Sub tbEdit_Click(sender As Object, e As EventArgs) Handles tbEdit.Click
        modDbAction("Edit")
    End Sub

    Private Sub tbView_Click(sender As Object, e As EventArgs) Handles tbView.Click
        modDbAction("View")
    End Sub

    Private Sub tbDelete_Click(sender As Object, e As EventArgs) Handles tbDelete.Click
        modDbAction("Delete")
    End Sub



    Private Sub showJembatanTimbang()
        Dim f As frmJembatanTimbang = frmJembatanTimbang.CreateInstance

        Dim dbAction As MsgHandler = New MsgHandler(AddressOf f.DbAction)
        modDbAction = dbAction

        f.MdiParent = Me

        ShowManage(f)
    End Sub

    Private Sub showSettings()
        ' Create and show settings form
        Dim f As New frmSettings()
        f.ShowDialog()
    End Sub

    Private Sub SettingsToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles SettingsToolStripMenuItem.Click
        showSettings()
    End Sub

    Private Sub showRole()
        Dim f As New frmRole()
        f.MdiParent = Me
        ShowManage(f)
    End Sub

    Private Sub showUser()
        Dim f As New frmUser()
        f.MdiParent = Me
        ShowManage(f)
    End Sub



    Private Sub SecurityToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles SecurityToolStripMenuItem.Click
        ' Tidak perlu melakukan apa-apa karena ini hanya menu dropdown
    End Sub

    ' Fungsi untuk logout
    Private Sub LogoutToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles LogoutToolStripMenuItem.Click
        If MessageBox.Show("Apakah Anda yakin ingin logout?", "Konfirmasi Logout", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            ' Logout user
            SecurityHelper.Logout()
            
            ' Tutup semua form
            For Each f As Form In Me.MdiChildren
                f.Close()
            Next
            
            ' Restart aplikasi untuk menampilkan form login
            Application.Restart()
        End If
    End Sub

    ' MainForm_Load sudah digabungkan dengan metode di atas

    Private Sub UpdateUserInfo()
        ' Tampilkan username di status bar
        If SecurityHelper.IsLoggedIn() Then
            lblUserInfo.Text = $"User: {SecurityHelper.GetCurrentUsername()}"
        Else
            lblUserInfo.Text = "User: Not logged in"
        End If
    End Sub

    Private Sub SetMenuAccess()
        ' Set akses menu berdasarkan role user menggunakan sistem database
        Try
            ' Build menu dinamis berdasarkan database
            ' MainForm uses ToolStrip, so we use the ToolStrip-compatible method
            MenuHelper.BuildToolStripMenu(ToolStrip1)

            ' Set akses untuk menu yang masih hardcoded (jika ada)
            ' === SECURITY MENU ===
            ' Menu User Management
            If UserToolStripMenuItem IsNot Nothing Then
                UserToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("USER", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' Menu Role Management
            If RoleToolStripMenuItem IsNot Nothing Then
                RoleToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("ROLE", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' Menu Role Menu Access
            If RoleMenuAccessToolStripMenuItem IsNot Nothing Then
                RoleMenuAccessToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("ROLE_ACCESS", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' Menu Menu Management
            If MenuToolStripMenuItem IsNot Nothing Then
                MenuToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("MENU", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' Security parent menu - visible jika ada akses ke salah satu child menu
            If SecurityToolStripMenuItem IsNot Nothing Then
                SecurityToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("USER", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("ROLE", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("ROLE_ACCESS", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("MENU", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' === TRANSAKSI MENU ===
            ' Menu Transaksi/Penimbangan
            If ToolStripButton1 IsNot Nothing Then
                ToolStripButton1.Visible = SecurityHelper.HasMenuAccess("PENIMBANGAN", SecurityHelper.AccessRight.ViewOnly)
            End If

            If PenimbanganToolStripMenuItem IsNot Nothing Then
                PenimbanganToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("PENIMBANGAN", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' === MASTER DATA MENU ===
            ' Master Data parent menu
            If ToolStripDropDownButton2 IsNot Nothing Then
                ToolStripDropDownButton2.Visible = SecurityHelper.HasMenuAccess("KEMASAN", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("KENDARAAN", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("MITRA", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("TIPE_KENDARAAN", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' Individual Master Data menu items
            If KemasanToolStripMenuItem IsNot Nothing Then
                KemasanToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("KEMASAN", SecurityHelper.AccessRight.ViewOnly)
            End If

            If KendaraanToolStripMenuItem IsNot Nothing Then
                KendaraanToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("KENDARAAN", SecurityHelper.AccessRight.ViewOnly)
            End If

            If MitraToolStripMenuItem IsNot Nothing Then
                MitraToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("MITRA", SecurityHelper.AccessRight.ViewOnly)
            End If

            If TipeKendaraanToolStripMenuItem IsNot Nothing Then
                TipeKendaraanToolStripMenuItem.Visible = SecurityHelper.HasMenuAccess("TIPE_KENDARAAN", SecurityHelper.AccessRight.ViewOnly)
            End If

            ' === ADMINISTRASI MENU ===
            ' Administrasi parent menu - visible jika ada akses ke salah satu child menu
            If ToolStripDropDownButton1 IsNot Nothing Then
                ToolStripDropDownButton1.Visible = SecurityHelper.HasMenuAccess("USER", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("ROLE", SecurityHelper.AccessRight.ViewOnly) OrElse
                                                   SecurityHelper.HasMenuAccess("SETTING", SecurityHelper.AccessRight.ViewOnly)
            End If

        Catch ex As Exception
            MessageBox.Show($"Error setting menu access: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Event handlers untuk menu items dengan access control
    Private Sub UserToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles UserToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("USER", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form User Management menggunakan method yang sudah ada
            showUser()
            ShowToolbars(New Boolean() {False, False, False, False, False, False})
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu User Management.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub RoleToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles RoleToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("ROLE", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Role Management (form biasa untuk manage role)
            showRole()
            ShowToolbars(New Boolean() {False, False, False, False, False, False})
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Role Management.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub RoleMenuAccessToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles RoleMenuAccessToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("ROLE_ACCESS", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Role Menu Access (untuk manage menu access per role)
            Try
                Dim frmRoleAccess As New frmRoleMenuAccess()
                frmRoleAccess.ShowDialog()
                ShowToolbars(New Boolean() {False, False, False, False, False, False})
            Catch ex As Exception
                MessageBox.Show($"Error membuka form Role Menu Access: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Role Menu Access.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub MenuToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles MenuToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("MENU", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Menu Management (untuk manage menu CRUD)
            Try
                Dim frmMenuManagement As New frmMenu()
                frmMenuManagement.MdiParent = Me
                frmMenuManagement.Show()
                frmMenuManagement.WindowState = FormWindowState.Maximized
                ShowToolbars(New Boolean() {True, True, True, True, True, True})
            Catch ex As Exception
                MessageBox.Show($"Error membuka form Menu Management: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Menu Management.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub PenimbanganToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles PenimbanganToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("PENIMBANGAN", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Jembatan Timbang menggunakan method yang sudah ada
            showJembatanTimbang()
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Penimbangan.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub KemasanToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles KemasanToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("KEMASAN", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Kemasan menggunakan method yang sudah ada
            showKemasan()
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Kemasan.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub KendaraanToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles KendaraanToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("KENDARAAN", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Kendaraan menggunakan method yang sudah ada
            showKendaraan()
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Kendaraan.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub MitraToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles MitraToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("MITRA", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Mitra menggunakan method yang sudah ada
            showMitra()
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Mitra.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub

    Private Sub TipeKendaraanToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles TipeKendaraanToolStripMenuItem.Click
        If SecurityHelper.HasMenuAccess("TIPE_KENDARAAN", SecurityHelper.AccessRight.ViewOnly) Then
            ' Buka form Tipe Kendaraan menggunakan method yang sudah ada
            showTipeKendaraan()
        Else
            MessageBox.Show("Anda tidak memiliki akses untuk membuka menu Tipe Kendaraan.", "Akses Ditolak", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End If
    End Sub
End Class