-- Skrip SQL Sederhana untuk membuat tabel user dan role

-- Buat tabel role jika belum ada
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'tm_role')
BEGIN
    CREATE TABLE [dbo].[tm_role](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [KodeRole] [varchar](20) NOT NULL,
        [NamaRole] [varchar](50) NOT NULL,
        [<PERSON>k<PERSON><PERSON>] [varchar](255) NULL,
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [LastUpdate] [datetime] NOT NULL DEFAULT(GETDATE()),
        CONSTRAINT [PK_tm_role] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
    
    PRINT 'Tabel tm_role berhasil dibuat'
END
ELSE
BEGIN
    PRINT 'Tabel tm_role sudah ada'
END
GO

-- Buat tabel user jika belum ada
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'tm_user')
BEGIN
    CREATE TABLE [dbo].[tm_user](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Username] [varchar](50) NOT NULL,
        [Password] [varchar](255) NOT NULL,
        [NamaLengkap] [varchar](100) NOT NULL,
        [Email] [varchar](100) NULL,
        [NoTelp] [varchar](20) NULL,
        [Role_id] [int] NOT NULL,
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [LastLogin] [datetime] NULL,
        [LastUpdate] [datetime] NOT NULL DEFAULT(GETDATE()),
        CONSTRAINT [PK_tm_user] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_tm_user_Username] UNIQUE ([Username])
    )
    
    PRINT 'Tabel tm_user berhasil dibuat'
END
ELSE
BEGIN
    PRINT 'Tabel tm_user sudah ada'
END
GO

-- Tambahkan foreign key dari user ke role jika belum ada
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'tm_user')
AND EXISTS (SELECT * FROM sys.tables WHERE name = 'tm_role')
AND NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_tm_user_tm_role')
BEGIN
    ALTER TABLE [dbo].[tm_user] WITH CHECK ADD CONSTRAINT [FK_tm_user_tm_role] 
    FOREIGN KEY([Role_id]) REFERENCES [dbo].[tm_role] ([Id])
    
    ALTER TABLE [dbo].[tm_user] CHECK CONSTRAINT [FK_tm_user_tm_role]
    
    PRINT 'Foreign key FK_tm_user_tm_role berhasil dibuat'
END
ELSE
BEGIN
    PRINT 'Foreign key FK_tm_user_tm_role sudah ada atau tabel belum dibuat'
END
GO

-- Tambahkan data awal untuk role jika belum ada
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'tm_role')
AND NOT EXISTS (SELECT * FROM [dbo].[tm_role] WHERE [KodeRole] = 'ADMIN')
BEGIN
    INSERT INTO [dbo].[tm_role] ([KodeRole], [NamaRole], [Deskripsi], [IsActive], [LastUpdate])
    VALUES 
        ('ADMIN', 'Administrator', 'Akses penuh ke semua fitur sistem', 1, GETDATE()),
        ('OPERATOR', 'Operator', 'Akses ke transaksi jembatan timbang', 1, GETDATE()),
        ('SUPERVISOR', 'Supervisor', 'Akses ke laporan dan monitoring', 1, GETDATE())
    
    PRINT 'Data awal role berhasil ditambahkan'
END
ELSE
BEGIN
    PRINT 'Data awal role sudah ada atau tabel belum dibuat'
END
GO

-- Tambahkan user admin default jika belum ada
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'tm_user')
AND EXISTS (SELECT * FROM [dbo].[tm_role] WHERE [KodeRole] = 'ADMIN')
AND NOT EXISTS (SELECT * FROM [dbo].[tm_user] WHERE [Username] = 'admin')
BEGIN
    DECLARE @AdminRoleId int
    SELECT @AdminRoleId = [Id] FROM [dbo].[tm_role] WHERE [KodeRole] = 'ADMIN'
    
    -- Password default: admin123 (dalam implementasi nyata sebaiknya di-hash)
    INSERT INTO [dbo].[tm_user] ([Username], [Password], [NamaLengkap], [Email], [Role_id], [IsActive], [LastUpdate])
    VALUES ('admin', 'admin123', 'Administrator Sistem', '<EMAIL>', @AdminRoleId, 1, GETDATE())
    
    PRINT 'User admin default berhasil ditambahkan'
END
ELSE
BEGIN
    PRINT 'User admin default sudah ada atau tabel belum dibuat'
END
GO
