$filePath = 'D:\SourceCode\Bright\DX24_1\DxJBT2\DxJBT2\Forms\Transaksi\frmJembatanTimbang_edit.vb'
$content = Get-Content $filePath
$insertIndex = 218
$newLines = @(
    '',
    '            '' Validate exit weight for exit weighing',
    '            If o.<PERSON> <= 0 Then',
    '                MessageBox.Show("Berat Keluar harus lebih besar dari 0 untuk penimbangan keluar", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)',
    '                BeratKeluarTextEdit.Focus()',
    '                Return',
    '            End If'
)

$newContent = @()
for ($i = 0; $i -lt $insertIndex; $i++) {
    $newContent += $content[$i]
}

foreach ($line in $newLines) {
    $newContent += $line
}

for ($i = $insertIndex; $i -lt $content.Length; $i++) {
    $newContent += $content[$i]
}

$newContent | Set-Content $filePath
