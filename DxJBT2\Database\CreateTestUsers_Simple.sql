-- <PERSON>ript untuk membuat test users dengan role yang berbeda (<PERSON><PERSON><PERSON>)
-- Jalankan script ini jika tabel belum memiliki kolom CreatedBy/CreatedDate

-- <PERSON>ikan ada role yang diperlukan (menggunakan kolom yang minimal)
IF NOT EXISTS (SELECT 1 FROM tm_role WHERE Id = 1)
BEGIN
    SET IDENTITY_INSERT tm_role ON
    INSERT INTO tm_role (Id, <PERSON>a<PERSON>ole, <PERSON>kripsi, IsActive) 
    VALUES (1, 'Administrator', 'Full access to all system features', 1)
    SET IDENTITY_INSERT tm_role OFF
END

IF NOT EXISTS (SELECT 1 FROM tm_role WHERE Id = 2)
BEGIN
    SET IDENTITY_INSERT tm_role ON
    INSERT INTO tm_role (Id, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>si, IsActive) 
    VALUES (2, 'Operator', 'Limited access for daily operations', 1)
    SET IDENTITY_INSERT tm_role OFF
END

IF NOT EXISTS (SELECT 1 FROM tm_role WHERE Id = 3)
BEGIN
    SET IDENTITY_INSERT tm_role ON
    INSERT INTO tm_role (I<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Active) 
    VALUES (3, 'Viewer', 'Read-only access for viewing data', 1)
    SET IDENTITY_INSERT tm_role OFF
END

-- Buat test users (menggunakan kolom yang minimal)
-- Admin User
IF NOT EXISTS (SELECT 1 FROM tm_user WHERE Username = 'admin')
BEGIN
    INSERT INTO tm_user (Username, Password, NamaLengkap, Email, Role_id, IsActive)
    VALUES ('admin', 'admin123', 'Administrator', '<EMAIL>', 1, 1)
END

-- Operator User  
IF NOT EXISTS (SELECT 1 FROM tm_user WHERE Username = 'operator')
BEGIN
    INSERT INTO tm_user (Username, Password, NamaLengkap, Email, Role_id, IsActive)
    VALUES ('operator', 'operator123', 'Operator User', '<EMAIL>', 2, 1)
END

-- Viewer User
IF NOT EXISTS (SELECT 1 FROM tm_user WHERE Username = 'viewer')
BEGIN
    INSERT INTO tm_user (Username, Password, NamaLengkap, Email, Role_id, IsActive)
    VALUES ('viewer', 'viewer123', 'Viewer User', '<EMAIL>', 3, 1)
END

-- Test User tanpa role (untuk testing error handling)
IF NOT EXISTS (SELECT 1 FROM tm_user WHERE Username = 'norole')
BEGIN
    INSERT INTO tm_user (Username, Password, NamaLengkap, Email, Role_id, IsActive)
    VALUES ('norole', 'norole123', 'No Role User', '<EMAIL>', NULL, 1)
END

-- Tampilkan hasil
SELECT 'Test Users Created' as Status;

SELECT 
    u.Username,
    u.NamaLengkap,
    r.NamaRole,
    u.IsActive
FROM tm_user u
LEFT JOIN tm_role r ON u.Role_id = r.Id
WHERE u.Username IN ('admin', 'operator', 'viewer', 'norole')
ORDER BY u.Username;

-- Tampilkan menu access untuk setiap role
SELECT 
    'Role Menu Access Summary' as Info;

SELECT 
    r.NamaRole,
    COUNT(rma.Menu_id) as TotalMenuAccess,
    SUM(CASE WHEN rma.CanView = 1 THEN 1 ELSE 0 END) as CanView,
    SUM(CASE WHEN rma.CanAdd = 1 THEN 1 ELSE 0 END) as CanAdd,
    SUM(CASE WHEN rma.CanEdit = 1 THEN 1 ELSE 0 END) as CanEdit,
    SUM(CASE WHEN rma.CanDelete = 1 THEN 1 ELSE 0 END) as CanDelete,
    SUM(CASE WHEN rma.CanPrint = 1 THEN 1 ELSE 0 END) as CanPrint
FROM tm_role r
LEFT JOIN tm_role_menu_access rma ON r.Id = rma.Role_id
WHERE r.IsActive = 1
GROUP BY r.Id, r.NamaRole
ORDER BY r.Id;

-- Detail menu access per role
SELECT 
    'Detailed Menu Access' as Info;

SELECT 
    r.NamaRole,
    m.KodeMenu,
    m.NamaMenu,
    CASE WHEN rma.CanView = 1 THEN 'Yes' ELSE 'No' END as CanView,
    CASE WHEN rma.CanAdd = 1 THEN 'Yes' ELSE 'No' END as CanAdd,
    CASE WHEN rma.CanEdit = 1 THEN 'Yes' ELSE 'No' END as CanEdit,
    CASE WHEN rma.CanDelete = 1 THEN 'Yes' ELSE 'No' END as CanDelete,
    CASE WHEN rma.CanPrint = 1 THEN 'Yes' ELSE 'No' END as CanPrint
FROM tm_role r
INNER JOIN tm_role_menu_access rma ON r.Id = rma.Role_id
INNER JOIN tm_menu m ON rma.Menu_id = m.Id
WHERE r.IsActive = 1 AND m.IsActive = 1
ORDER BY r.NamaRole, m.MenuOrder;

-- Test query untuk simulasi login
SELECT 
    'Login Test Queries' as Info;

-- Test login admin
SELECT 
    'Admin Login Test' as TestType,
    u.Username,
    u.NamaLengkap,
    r.NamaRole,
    CASE WHEN r.NamaRole = 'Administrator' THEN 'Full Access (Bypass)' ELSE 'Limited Access' END as AccessLevel
FROM tm_user u
LEFT JOIN tm_role r ON u.Role_id = r.Id
WHERE u.Username = 'admin' AND u.Password = 'admin123' AND u.IsActive = 1;

-- Test login operator
SELECT 
    'Operator Login Test' as TestType,
    u.Username,
    u.NamaLengkap,
    r.NamaRole,
    COUNT(rma.Menu_id) as MenuAccessCount
FROM tm_user u
LEFT JOIN tm_role r ON u.Role_id = r.Id
LEFT JOIN tm_role_menu_access rma ON r.Id = rma.Role_id AND rma.CanView = 1
WHERE u.Username = 'operator' AND u.Password = 'operator123' AND u.IsActive = 1
GROUP BY u.Username, u.NamaLengkap, r.NamaRole;

-- Test login viewer
SELECT 
    'Viewer Login Test' as TestType,
    u.Username,
    u.NamaLengkap,
    r.NamaRole,
    COUNT(rma.Menu_id) as MenuAccessCount
FROM tm_user u
LEFT JOIN tm_role r ON u.Role_id = r.Id
LEFT JOIN tm_role_menu_access rma ON r.Id = rma.Role_id AND rma.CanView = 1
WHERE u.Username = 'viewer' AND u.Password = 'viewer123' AND u.IsActive = 1
GROUP BY u.Username, u.NamaLengkap, r.NamaRole;
