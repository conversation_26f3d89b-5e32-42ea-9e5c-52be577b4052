# Script untuk membuat ikon administrasi dengan simbol dokumen/folder
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

# Fungsi untuk membuat gambar dokumen sebagai simbol administrasi
function New-AdminIcon {
    param (
        [string]$outputPath,
        [System.Drawing.Color]$backgroundColor,
        [System.Drawing.Color]$documentColor
    )
    
    try {
        # Buat bitmap 32x32 pixels dengan resolusi tinggi
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Aktifkan anti-aliasing untuk gambar yang lebih halus
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        
        # Isi dengan warna latar belakang
        $graphics.Clear($backgroundColor)
        
        # Buat brush untuk warna dokumen
        $docBrush = New-Object System.Drawing.SolidBrush($documentColor)
        $docPen = New-Object System.Drawing.Pen($documentColor, 1)
        
        # Gambar folder/dokumen
        # Koordinat untuk dokumen utama
        $docX = 8
        $docY = 8
        $docWidth = 16
        $docHeight = 20
        
        # Gambar dokumen utama (persegi panjang)
        $docRect = New-Object System.Drawing.Rectangle($docX, $docY, $docWidth, $docHeight)
        $graphics.FillRectangle($docBrush, $docRect)
        
        # Gambar garis-garis dokumen (seperti teks)
        $lineY1 = $docY + 4
        $lineY2 = $docY + 8
        $lineY3 = $docY + 12
        $lineY4 = $docY + 16
        $lineX1 = $docX + 2
        $lineLength = $docWidth - 4
        
        # Gambar garis putih untuk menunjukkan teks pada dokumen
        $linePen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(230, 230, 230), 1)
        $graphics.DrawLine($linePen, $lineX1, $lineY1, $lineX1 + $lineLength, $lineY1)
        $graphics.DrawLine($linePen, $lineX1, $lineY2, $lineX1 + $lineLength, $lineY2)
        $graphics.DrawLine($linePen, $lineX1, $lineY3, $lineX1 + $lineLength, $lineY3)
        $graphics.DrawLine($linePen, $lineX1, $lineY4, $lineX1 + $lineLength - 6, $lineY4)
        
        # Gambar dokumen kedua di belakang (untuk efek tumpukan)
        $doc2X = $docX - 2
        $doc2Y = $docY - 2
        $doc2Pen = New-Object System.Drawing.Pen($documentColor, 1.5)
        $doc2Rect = New-Object System.Drawing.Rectangle($doc2X, $doc2Y, $docWidth, $docHeight)
        $graphics.DrawRectangle($doc2Pen, $doc2Rect)
        
        # Simpan gambar
        $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Bersihkan resources
        $linePen.Dispose()
        $docPen.Dispose()
        $doc2Pen.Dispose()
        $docBrush.Dispose()
        $graphics.Dispose()
        $bitmap.Dispose()
        
        Write-Host "Gambar administrasi berhasil dibuat: $outputPath"
    }
    catch {
        Write-Host "Error saat membuat gambar administrasi: $_"
    }
}

# Buat direktori Resources jika belum ada
$resourcesDir = "d:\SourceCode\Bright\DX24_1\DxJBT2\DxJBT2\Resources"
if (-not (Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir
}

# Buat ikon untuk menu Administrasi
$adminPath = Join-Path $resourcesDir "Administrasi_32x32.png"
New-AdminIcon -outputPath $adminPath -backgroundColor ([System.Drawing.Color]::FromArgb(41, 128, 185)) -documentColor ([System.Drawing.Color]::White)

Write-Host "Pembuatan ikon administrasi selesai."
