﻿Public Class frmEntryBase
    Public isClosing As Boolean = False
    Public Delegate Sub MsgHandler(ByVal msg As String)

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        isClosing = True
        Dim dr As DialogResult = MessageBox.Show("Are you sure you want to Exit?", "Exit", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

        If dr = Windows.Forms.DialogResult.Yes Then
            Close()
        End If
    End Sub

    Private Sub frmEntryBase_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If Not isClosing Then
            e.Cancel = True
            Return
        End If
        isClosing = False
    End Sub

    Private Sub frmEntryBase_KeyDown(sender As Object, e As KeyEventArgs) Handles Me.KeyDown
        If e.KeyCode = Keys.Escape Then
            isClosing = True
            Close()
        End If
    End Sub
    Public Function CekNullNumber(ByVal pData As Object) As Double
        If pData Is DBNull.Value Then
            Return 0
        End If
        Return Convert.ToDouble(pData)
    End Function
    Public Function CekNullGuid(ByVal pData As Object) As Guid
        If pData Is DBNull.Value Then
            Return Guid.Empty
        End If
        Return pData
    End Function
End Class