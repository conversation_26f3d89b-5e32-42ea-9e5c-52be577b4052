Imports DxJBT2.Helpers

' Base form class yang menyediakan fungsi access control otomatis
Public Class BaseFormWithAccess
    Inherits Form

    ' Property untuk menyimpan kode menu
    Public Property MenuCode As String

    ' Property untuk menyimpan informasi access rights
    Public Property CanAdd As Boolean = False
    Public Property CanEdit As Boolean = False
    Public Property CanDelete As Boolean = False
    Public Property CanPrint As Boolean = False

    ' Event yang dipanggil saat access rights berubah
    Public Event AccessRightsChanged()

    Public Sub New()
        MyBase.New()
    End Sub

    ' Override method Load untuk set access rights
    Protected Overrides Sub OnLoad(e As EventArgs)
        MyBase.OnLoad(e)
        
        ' Set access rights berdasarkan menu code
        If Not String.IsNullOrEmpty(MenuCode) Then
            SetAccessRights()
        End If
    End Sub

    ' Method untuk set access rights berdasarkan menu code
    Protected Sub SetAccessRights()
        Try
            If String.IsNullOrEmpty(MenuCode) Then
                Return
            End If

            ' Cek access rights dari SecurityHelper
            CanAdd = SecurityHelper.HasMenuAccess(MenuCode, SecurityHelper.AccessRight.CanAdd)
            CanEdit = SecurityHelper.HasMenuAccess(MenuCode, SecurityHelper.AccessRight.CanEdit)
            CanDelete = SecurityHelper.HasMenuAccess(MenuCode, SecurityHelper.AccessRight.CanDelete)
            CanPrint = SecurityHelper.HasMenuAccess(MenuCode, SecurityHelper.AccessRight.CanPrint)

            ' Apply access rights ke controls
            ApplyAccessRights()

            ' Trigger event
            RaiseEvent AccessRightsChanged()

        Catch ex As Exception
            ' Log error tapi jangan tampilkan ke user
            System.Diagnostics.Debug.WriteLine($"Error setting access rights: {ex.Message}")
        End Try
    End Sub

    ' Method untuk apply access rights ke controls
    Protected Overridable Sub ApplyAccessRights()
        ' Set access untuk button Add/New/Tambah
        SetControlEnabled(CanAdd, "btnAdd", "btnNew", "btnTambah", "toolStripButtonAdd", "toolStripButtonNew")
        
        ' Set access untuk button Edit/Ubah/Modify
        SetControlEnabled(CanEdit, "btnEdit", "btnUbah", "btnModify", "toolStripButtonEdit", "toolStripButtonModify")
        
        ' Set access untuk button Delete/Hapus/Remove
        SetControlEnabled(CanDelete, "btnDelete", "btnHapus", "btnRemove", "toolStripButtonDelete", "toolStripButtonRemove")
        
        ' Set access untuk button Print/Cetak/Report
        SetControlEnabled(CanPrint, "btnPrint", "btnCetak", "btnReport", "toolStripButtonPrint", "toolStripButtonReport")
    End Sub

    ' Helper method untuk set enabled status control berdasarkan nama
    Protected Sub SetControlEnabled(enabled As Boolean, ParamArray controlNames As String())
        For Each controlName In controlNames
            Dim control = FindControlByName(Me, controlName)
            If control IsNot Nothing Then
                control.Enabled = enabled
                Exit For ' Keluar setelah menemukan control pertama yang cocok
            End If
        Next
    End Sub

    ' Helper method untuk mencari control berdasarkan nama secara rekursif
    Protected Function FindControlByName(parent As Control, controlName As String) As Control
        ' Cek apakah parent control adalah yang dicari
        If parent.Name.Equals(controlName, StringComparison.OrdinalIgnoreCase) Then
            Return parent
        End If

        ' Cari di child controls
        For Each child As Control In parent.Controls
            Dim found = FindControlByName(child, controlName)
            If found IsNot Nothing Then
                Return found
            End If
        Next

        ' Cari di ToolStrip items jika parent adalah ToolStrip
        If TypeOf parent Is ToolStrip Then
            Dim toolStrip = DirectCast(parent, ToolStrip)
            For Each item As ToolStripItem In toolStrip.Items
                If item.Name.Equals(controlName, StringComparison.OrdinalIgnoreCase) Then
                    Return item
                End If
            Next
        End If

        ' Cari di MenuStrip items jika parent adalah MenuStrip
        If TypeOf parent Is MenuStrip Then
            Dim menuStrip = DirectCast(parent, MenuStrip)
            For Each item As ToolStripMenuItem In menuStrip.Items
                If item.Name.Equals(controlName, StringComparison.OrdinalIgnoreCase) Then
                    Return item
                End If
                ' Cari di dropdown items
                Dim found = FindMenuItemByName(item, controlName)
                If found IsNot Nothing Then
                    Return found
                End If
            Next
        End If

        Return Nothing
    End Function

    ' Helper method untuk mencari menu item berdasarkan nama
    Private Function FindMenuItemByName(parentItem As ToolStripMenuItem, controlName As String) As ToolStripMenuItem
        For Each item As ToolStripItem In parentItem.DropDownItems
            If TypeOf item Is ToolStripMenuItem Then
                Dim menuItem = DirectCast(item, ToolStripMenuItem)
                If menuItem.Name.Equals(controlName, StringComparison.OrdinalIgnoreCase) Then
                    Return menuItem
                End If
                ' Recursive search
                Dim found = FindMenuItemByName(menuItem, controlName)
                If found IsNot Nothing Then
                    Return found
                End If
            End If
        Next
        Return Nothing
    End Function

    ' Method untuk refresh access rights (dipanggil jika ada perubahan role/permission)
    Public Sub RefreshAccessRights()
        SetAccessRights()
    End Sub

    ' Method untuk cek apakah user punya akses tertentu
    Public Function HasAccess(accessRight As SecurityHelper.AccessRight) As Boolean
        If String.IsNullOrEmpty(MenuCode) Then
            Return False
        End If
        
        Return SecurityHelper.HasMenuAccess(MenuCode, accessRight)
    End Function

    ' Method untuk menampilkan pesan akses ditolak
    Protected Sub ShowAccessDeniedMessage(action As String)
        MessageBox.Show($"Anda tidak memiliki akses untuk {action} pada menu ini.", 
                       "Akses Ditolak", 
                       MessageBoxButtons.OK, 
                       MessageBoxIcon.Warning)
    End Sub

    ' Method untuk validasi akses sebelum melakukan aksi
    Protected Function ValidateAccess(accessRight As SecurityHelper.AccessRight, actionName As String) As Boolean
        If HasAccess(accessRight) Then
            Return True
        Else
            ShowAccessDeniedMessage(actionName)
            Return False
        End If
    End Function

    ' Override method untuk handle button clicks dengan validasi akses
    Protected Sub HandleAddClick()
        If ValidateAccess(SecurityHelper.AccessRight.CanAdd, "menambah data") Then
            OnAddClick()
        End If
    End Sub

    Protected Sub HandleEditClick()
        If ValidateAccess(SecurityHelper.AccessRight.CanEdit, "mengedit data") Then
            OnEditClick()
        End If
    End Sub

    Protected Sub HandleDeleteClick()
        If ValidateAccess(SecurityHelper.AccessRight.CanDelete, "menghapus data") Then
            OnDeleteClick()
        End If
    End Sub

    Protected Sub HandlePrintClick()
        If ValidateAccess(SecurityHelper.AccessRight.CanPrint, "mencetak data") Then
            OnPrintClick()
        End If
    End Sub

    ' Virtual methods yang bisa di-override oleh child classes
    Protected Overridable Sub OnAddClick()
        ' Override di child class
    End Sub

    Protected Overridable Sub OnEditClick()
        ' Override di child class
    End Sub

    Protected Overridable Sub OnDeleteClick()
        ' Override di child class
    End Sub

    Protected Overridable Sub OnPrintClick()
        ' Override di child class
    End Sub

    ' Method untuk set menu code dan refresh access rights
    Public Sub SetMenuCode(menuCode As String)
        Me.MenuCode = menuCode
        If Me.IsHandleCreated Then
            SetAccessRights()
        End If
    End Sub
End Class
