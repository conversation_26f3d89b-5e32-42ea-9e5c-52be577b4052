Imports System.Drawing
Imports System.Drawing.Printing
Imports System.Text

' Data Transfer Object untuk slip timbangan
Public Class WeighingSlipData
    Public Property NoTransaksi As String
    Public Property TanggalMasuk As DateTime
    Public Property TanggalKeluar As DateTime?
    Public Property NoPolisi As String
    Public Property NamaSupir As String
    Public Property NamaMitra As String
    Public Property AsalMuatan As String
    Public Property TujuanMuatan As String
    Public Property BeratMasuk As Decimal
    Public Property BeratKeluar As Decimal?
    Public Property BeratNetto As Decimal?
    Public Property TotalBeratKemasan As Decimal?
    Public Property BeratNettoMurni As Decimal?
    Public Property StatusTransaksi As String
    Public Property Keterangan As String
    Public Property InfoKemasan As String
End Class

Public Class WeighingSlipReport
    Private _slipData As WeighingSlipData
    Private _companyName As String = "PT. SISTEM JEMBATAN TIMBANG"
    Private _companyAddress As String = "Jl. Industri No. 123, Jakarta"
    Private _companyPhone As String = "Telp: (021) 1234-5678"

    Public Sub New(slipData As WeighingSlipData)
        _slipData = slipData
    End Sub

    Public Sub PrintSlip()
        Try
            Dim printDoc As New PrintDocument()
            printDoc.DocumentName = "Slip Timbangan - " & _slipData.NoTransaksi

            ' Set printer settings for slip printer (usually 80mm thermal printer)
            Dim printerSettings As New PrinterSettings()
            printDoc.PrinterSettings = printerSettings

            ' Set page settings for thermal slip (80mm width)
            Dim pageSettings As New PageSettings()
            pageSettings.PaperSize = New PaperSize("Slip", 315, 600) ' 80mm x 150mm in 1/100 inch
            pageSettings.Margins = New Margins(10, 10, 10, 10)
            printDoc.DefaultPageSettings = pageSettings

            AddHandler printDoc.PrintPage, AddressOf PrintSlipPage

            ' Show print preview dialog
            Dim printPreview As New PrintPreviewDialog()
            printPreview.Document = printDoc
            printPreview.WindowState = FormWindowState.Maximized
            printPreview.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("Error saat mencetak slip: " & ex.Message, "Print Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub PrintSlipPage(sender As Object, e As PrintPageEventArgs)
        Dim g As Graphics = e.Graphics
        Dim font As New Font("Courier New", 8, FontStyle.Regular)
        Dim fontBold As New Font("Courier New", 8, FontStyle.Bold)
        Dim fontTitle As New Font("Courier New", 10, FontStyle.Bold)
        Dim brush As New SolidBrush(Color.Black)

        Dim yPos As Integer = 10
        Dim lineHeight As Integer = 15
        Dim leftMargin As Integer = 10
        Dim centerX As Integer = e.PageBounds.Width \ 2

        ' Helper function to draw centered text
        Dim drawCenteredText = Sub(text As String, fontParam As Font, y As Integer)
                                   Dim textSize = g.MeasureString(text, fontParam)
                                   g.DrawString(text, fontParam, brush, centerX - (textSize.Width / 2), y)
                               End Sub

        ' Helper function to draw left-aligned text
        Dim drawLeftText = Sub(text As String, fontParam As Font, y As Integer)
                               g.DrawString(text, fontParam, brush, leftMargin, y)
                           End Sub

        ' Header
        drawCenteredText(_companyName, fontTitle, yPos)
        yPos += lineHeight + 5
        drawCenteredText(_companyAddress, font, yPos)
        yPos += lineHeight
        drawCenteredText(_companyPhone, font, yPos)
        yPos += lineHeight + 10

        ' Separator line
        g.DrawLine(New Pen(Color.Black), leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos)
        yPos += 10

        ' Title
        drawCenteredText("SLIP TIMBANGAN", fontBold, yPos)
        yPos += lineHeight + 10

        ' Transaction details
        drawLeftText("No. Transaksi: " & _slipData.NoTransaksi, fontBold, yPos)
        yPos += lineHeight
        drawLeftText("Tanggal Masuk: " & _slipData.TanggalMasuk.ToString("dd/MM/yyyy HH:mm"), font, yPos)
        yPos += lineHeight

        If _slipData.TanggalKeluar.HasValue Then
            drawLeftText("Tanggal Keluar: " & _slipData.TanggalKeluar.Value.ToString("dd/MM/yyyy HH:mm"), font, yPos)
            yPos += lineHeight
        End If

        yPos += 5

        ' Vehicle details
        drawLeftText("No. Polisi: " & If(String.IsNullOrEmpty(_slipData.NoPolisi), "N/A", _slipData.NoPolisi), fontBold, yPos)
        yPos += lineHeight

        If Not String.IsNullOrEmpty(_slipData.NamaSupir) Then
            drawLeftText("Nama Supir: " & _slipData.NamaSupir, font, yPos)
            yPos += lineHeight
        End If

        If Not String.IsNullOrEmpty(_slipData.NamaMitra) Then
            drawLeftText("Mitra: " & _slipData.NamaMitra, font, yPos)
            yPos += lineHeight
        End If

        yPos += 5

        ' Weight details
        drawLeftText("Berat Masuk: " & _slipData.BeratMasuk.ToString("#,##0") & " kg", font, yPos)
        yPos += lineHeight

        If _slipData.BeratKeluar.HasValue Then
            drawLeftText("Berat Keluar: " & _slipData.BeratKeluar.Value.ToString("#,##0") & " kg", font, yPos)
            yPos += lineHeight
        End If

        If _slipData.BeratNetto.HasValue Then
            drawLeftText("Berat Netto: " & _slipData.BeratNetto.Value.ToString("#,##0") & " kg", fontBold, yPos)
            yPos += lineHeight
        End If

        ' Kemasan details
        If _slipData.TotalBeratKemasan.HasValue AndAlso _slipData.TotalBeratKemasan.Value > 0 Then
            yPos += 5
            drawLeftText("Berat Kemasan: " & _slipData.TotalBeratKemasan.Value.ToString("#,##0") & " kg", font, yPos)
            yPos += lineHeight

            If Not String.IsNullOrEmpty(_slipData.InfoKemasan) Then
                drawLeftText("Detail: " & _slipData.InfoKemasan, font, yPos)
                yPos += lineHeight
            End If

            If _slipData.BeratNettoMurni.HasValue Then
                drawLeftText("Berat Netto Murni: " & _slipData.BeratNettoMurni.Value.ToString("#,##0") & " kg", fontBold, yPos)
                yPos += lineHeight
            End If
        End If

        yPos += 5

        ' Additional details
        If Not String.IsNullOrEmpty(_slipData.AsalMuatan) Then
            drawLeftText("Asal Muatan: " & _slipData.AsalMuatan, font, yPos)
            yPos += lineHeight
        End If

        If Not String.IsNullOrEmpty(_slipData.TujuanMuatan) Then
            drawLeftText("Tujuan Muatan: " & _slipData.TujuanMuatan, font, yPos)
            yPos += lineHeight
        End If

        If Not String.IsNullOrEmpty(_slipData.Keterangan) Then
            drawLeftText("Keterangan: " & _slipData.Keterangan, font, yPos)
            yPos += lineHeight
        End If

        yPos += 10

        ' Separator line
        g.DrawLine(New Pen(Color.Black), leftMargin, yPos, e.PageBounds.Width - leftMargin, yPos)
        yPos += 10

        ' Footer
        drawCenteredText("Status: " & _slipData.StatusTransaksi, fontBold, yPos)
        yPos += lineHeight + 5
        drawCenteredText("Dicetak: " & DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), font, yPos)
        yPos += lineHeight + 10
        drawCenteredText("Terima kasih atas kunjungan Anda", font, yPos)

        ' Cleanup
        font.Dispose()
        fontBold.Dispose()
        fontTitle.Dispose()
        brush.Dispose()
    End Sub

    Public Sub PrintDirectly()
        Try
            Dim printDoc As New PrintDocument()
            printDoc.DocumentName = "Slip Timbangan - " & _slipData.NoTransaksi

            ' Set printer settings for slip printer
            Dim printerSettings As New PrinterSettings()
            printDoc.PrinterSettings = printerSettings

            ' Set page settings for thermal slip
            Dim pageSettings As New PageSettings()
            pageSettings.PaperSize = New PaperSize("Slip", 315, 600) ' 80mm x 150mm
            pageSettings.Margins = New Margins(10, 10, 10, 10)
            printDoc.DefaultPageSettings = pageSettings

            AddHandler printDoc.PrintPage, AddressOf PrintSlipPage

            ' Print directly without preview
            printDoc.Print()

        Catch ex As Exception
            MessageBox.Show("Error saat mencetak slip: " & ex.Message, "Print Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Helper method untuk mengkonversi entity ke DTO
    Public Shared Function CreateSlipData(transaction As tr_jembatan_timbang) As WeighingSlipData
        Dim slipData As New WeighingSlipData()

        With slipData
            .NoTransaksi = transaction.NoTransaksi
            .TanggalMasuk = transaction.TanggalMasuk
            .TanggalKeluar = transaction.TanggalKeluar
            .BeratMasuk = transaction.BeratMasuk
            .BeratKeluar = transaction.BeratKeluar
            .BeratNetto = transaction.BeratNetto
            .TotalBeratKemasan = transaction.TotalBeratKemasan
            .BeratNettoMurni = transaction.BeratNettoMurni
            .StatusTransaksi = transaction.StatusTransaksi
            .NamaSupir = transaction.NamaSupir
            .AsalMuatan = transaction.AsalMuatan
            .TujuanMuatan = transaction.TujuanMuatan
            .Keterangan = transaction.Keterangan

            ' Safely get related data
            .NoPolisi = If(transaction.tm_kendaraan IsNot Nothing, transaction.tm_kendaraan.NoPolisi, "N/A")
            .NamaMitra = If(transaction.tm_mitra IsNot Nothing, transaction.tm_mitra.NamaMitra, "")

            ' Get kemasan info
            If transaction.tr_kemasan_detail IsNot Nothing AndAlso transaction.tr_kemasan_detail.Count > 0 Then
                Dim totalQty = transaction.tr_kemasan_detail.Sum(Function(k) k.Qty)
                Dim jenisKemasan = transaction.tr_kemasan_detail.Count
                .InfoKemasan = $"{totalQty} kemasan ({jenisKemasan} jenis)"
            Else
                .InfoKemasan = ""
            End If
        End With

        Return slipData
    End Function
End Class
