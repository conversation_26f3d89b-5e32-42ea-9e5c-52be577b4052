Imports System.Data.Entity

Public Class frmRole
    Private _context As New dxjbt2Entities()
    Private _isNewRecord As Boolean = False
    Private _currentId As Integer = 0

    Private Sub frmRole_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        RefreshGrid()
        SetupForm()
    End Sub

    Private Sub SetupForm()
        ' Setup DataGridView
        dgvRole.AutoGenerateColumns = False
        dgvRole.Columns.Clear()

        ' Tambahkan kolom
        Dim idColumn As New DataGridViewTextBoxColumn()
        idColumn.DataPropertyName = "Id"
        idColumn.HeaderText = "ID"
        idColumn.Visible = False
        dgvRole.Columns.Add(idColumn)

        Dim kodeColumn As New DataGridViewTextBoxColumn()
        kodeColumn.DataPropertyName = "KodeRole"
        kodeColumn.HeaderText = "Kode Role"
        kodeColumn.Width = 100
        dgvRole.Columns.Add(kodeColumn)

        Dim namaColumn As New DataGridViewTextBoxColumn()
        namaColumn.DataPropertyName = "NamaRole"
        namaColumn.HeaderText = "Nama Role"
        namaColumn.Width = 150
        dgvRole.Columns.Add(namaColumn)

        Dim deskripsiColumn As New DataGridViewTextBoxColumn()
        deskripsiColumn.DataPropertyName = "Deskripsi"
        deskripsiColumn.HeaderText = "Deskripsi"
        deskripsiColumn.Width = 200
        dgvRole.Columns.Add(deskripsiColumn)

        Dim statusColumn As New DataGridViewCheckBoxColumn()
        statusColumn.DataPropertyName = "IsActive"
        statusColumn.HeaderText = "Aktif"
        statusColumn.Width = 50
        dgvRole.Columns.Add(statusColumn)

        ' Set mode awal
        SetFormMode(False)
    End Sub

    Private Sub RefreshGrid()
        Try
            dgvRole.DataSource = _context.tm_role.ToList()
        Catch ex As Exception
            MessageBox.Show($"Error saat memuat data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub SetFormMode(isEdit As Boolean)
        ' Atur mode form
        txtKodeRole.Enabled = isEdit
        txtNamaRole.Enabled = isEdit
        txtDeskripsi.Enabled = isEdit
        chkActive.Enabled = isEdit

        ' Atur tombol
        btnNew.Enabled = Not isEdit
        btnEdit.Enabled = Not isEdit And dgvRole.SelectedRows.Count > 0
        btnDelete.Enabled = Not isEdit And dgvRole.SelectedRows.Count > 0
        btnSave.Enabled = isEdit
        btnCancel.Enabled = isEdit

        ' Clear textbox jika mode baru
        If isEdit And _isNewRecord Then
            txtKodeRole.Text = ""
            txtNamaRole.Text = ""
            txtDeskripsi.Text = ""
            chkActive.Checked = True
        End If
    End Sub

    Private Sub LoadDataToForm(id As Integer)
        Try
            Dim role = _context.tm_role.Find(id)
            If role IsNot Nothing Then
                _currentId = role.Id
                txtKodeRole.Text = role.KodeRole
                txtNamaRole.Text = role.NamaRole
                txtDeskripsi.Text = role.Deskripsi
                chkActive.Checked = role.IsActive
            End If
        Catch ex As Exception
            MessageBox.Show($"Error saat memuat data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub dgvRole_SelectionChanged(sender As Object, e As EventArgs) Handles dgvRole.SelectionChanged
        If dgvRole.SelectedRows.Count > 0 Then
            Try
                ' Menggunakan indeks kolom 0 yang merupakan kolom Id
                Dim id As Integer = CInt(dgvRole.SelectedRows(0).Cells(0).Value)
                LoadDataToForm(id)
                btnEdit.Enabled = True
                btnDelete.Enabled = True
            Catch ex As Exception
                MessageBox.Show($"Error saat memilih data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                btnEdit.Enabled = False
                btnDelete.Enabled = False
            End Try
        Else
            btnEdit.Enabled = False
            btnDelete.Enabled = False
        End If
    End Sub

    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        _isNewRecord = True
        _currentId = 0
        SetFormMode(True)
        txtKodeRole.Focus()
    End Sub

    Private Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
        If dgvRole.SelectedRows.Count > 0 Then
            _isNewRecord = False
            SetFormMode(True)
            txtKodeRole.Focus()
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If ValidateForm() Then
            Try
                If _isNewRecord Then
                    ' Tambah baru
                    Dim newRole As New tm_role() With {
                        .KodeRole = txtKodeRole.Text.Trim(),
                        .NamaRole = txtNamaRole.Text.Trim(),
                        .Deskripsi = txtDeskripsi.Text.Trim(),
                        .IsActive = chkActive.Checked,
                        .LastUpdate = DateTime.Now
                    }
                    _context.tm_role.Add(newRole)
                Else
                    ' Update
                    Dim role = _context.tm_role.Find(_currentId)
                    If role IsNot Nothing Then
                        role.KodeRole = txtKodeRole.Text.Trim()
                        role.NamaRole = txtNamaRole.Text.Trim()
                        role.Deskripsi = txtDeskripsi.Text.Trim()
                        role.IsActive = chkActive.Checked
                        role.LastUpdate = DateTime.Now
                    End If
                End If

                _context.SaveChanges()
                MessageBox.Show("Data berhasil disimpan", "Informasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
                RefreshGrid()
                SetFormMode(False)
            Catch ex As Exception
                MessageBox.Show($"Error saat menyimpan data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Function ValidateForm() As Boolean
        If String.IsNullOrWhiteSpace(txtKodeRole.Text) Then
            MessageBox.Show("Kode Role harus diisi", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtKodeRole.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtNamaRole.Text) Then
            MessageBox.Show("Nama Role harus diisi", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNamaRole.Focus()
            Return False
        End If

        ' Validasi kode unik
        If _isNewRecord OrElse txtKodeRole.Text.Trim() <> _context.tm_role.Find(_currentId).KodeRole Then
            Dim existingRole = _context.tm_role.FirstOrDefault(Function(r) r.KodeRole = txtKodeRole.Text.Trim())
            If existingRole IsNot Nothing Then
                MessageBox.Show("Kode Role sudah digunakan", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtKodeRole.Focus()
                Return False
            End If
        End If

        Return True
    End Function

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If dgvRole.SelectedRows.Count > 0 Then
            Try
                ' Menggunakan indeks kolom 0 yang merupakan kolom Id
                Dim id As Integer = CInt(dgvRole.SelectedRows(0).Cells(0).Value)

                ' Periksa apakah role digunakan oleh user
                Dim usersWithRole = _context.tm_user.Count(Function(u) u.Role_id = id)
                If usersWithRole > 0 Then
                    MessageBox.Show($"Role ini digunakan oleh {usersWithRole} user. Tidak dapat dihapus.", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

                If MessageBox.Show("Apakah Anda yakin ingin menghapus data ini?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    Try
                        Dim role = _context.tm_role.Find(id)
                        If role IsNot Nothing Then
                            _context.tm_role.Remove(role)
                            _context.SaveChanges()
                            MessageBox.Show("Data berhasil dihapus", "Informasi", MessageBoxButtons.OK, MessageBoxIcon.Information)
                            RefreshGrid()
                        End If
                    Catch ex As Exception
                        MessageBox.Show($"Error saat menghapus data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    End Try
                End If
            Catch ex As Exception
                MessageBox.Show($"Error saat memilih data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        SetFormMode(False)
        If dgvRole.SelectedRows.Count > 0 Then
            Dim id As Integer = CInt(dgvRole.SelectedRows(0).Cells("Id").Value)
            LoadDataToForm(id)
        End If
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub
End Class
