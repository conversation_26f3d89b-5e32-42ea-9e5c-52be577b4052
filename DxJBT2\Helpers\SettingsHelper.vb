Imports DxJBT2
Imports System.IO.Ports

''' <summary>
''' Helper class to manage application settings
''' </summary>
Public Class SettingsHelper
    ''' <summary>
    ''' Initialize default settings if they don't exist
    ''' </summary>
    Public Shared Sub InitializeDefaultSettings()
        Try
            ' Check if COM port setting exists, if not create it
            If String.IsNullOrEmpty(GetSetting("WeighbridgeComPort", "")) Then
                ' Get available COM ports
                Dim ports As String() = SerialPort.GetPortNames()
                Dim defaultPort As String = "COM1"
                
                ' Use first available port if any
                If ports.Length > 0 Then
                    defaultPort = ports(0)
                End If
                
                SaveSetting("WeighbridgeComPort", defaultPort, "COM port for weighbridge connection")
            End If
            
            ' Check if baud rate setting exists, if not create it
            If String.IsNullOrEmpty(GetSetting("WeighbridgeBaudRate", "")) Then
                SaveSetting("WeighbridgeBaudRate", "9600", "Baud rate for weighbridge connection")
            End If
            
            ' Check if dummy mode setting exists, if not create it
            If String.IsNullOrEmpty(GetSetting("WeighbridgeDummyMode", "")) Then
                SaveSetting("WeighbridgeDummyMode", "False", "Enable dummy mode for development without hardware")
            End If
            
        Catch ex As Exception
            ' Log error
        End Try
    End Sub
    
    ''' <summary>
    ''' Get a setting value from the database
    ''' </summary>
    ''' <param name="settingName">Name of the setting</param>
    ''' <param name="defaultValue">Default value if setting not found</param>
    ''' <returns>Setting value or default if not found</returns>
    Public Shared Function GetSetting(settingName As String, defaultValue As String) As String
        Try
            Using dc As New dxjbt2Entities
                ' Check if setting exists
                Dim setting = dc.tm_settings.FirstOrDefault(Function(s) s.SettingName = settingName)
                
                If setting IsNot Nothing Then
                    Return setting.SettingValue
                Else
                    Return defaultValue
                End If
            End Using
        Catch ex As Exception
            ' Log error
            Return defaultValue
        End Try
    End Function
    
    ''' <summary>
    ''' Save a setting to the database
    ''' </summary>
    ''' <param name="settingName">Name of the setting</param>
    ''' <param name="settingValue">Value to save</param>
    ''' <param name="description">Optional description</param>
    ''' <returns>True if successful</returns>
    Public Shared Function SaveSetting(settingName As String, settingValue As String, Optional description As String = "") As Boolean
        Try
            Using dc As New dxjbt2Entities
                ' Check if setting exists
                Dim setting = dc.tm_settings.FirstOrDefault(Function(s) s.SettingName = settingName)
                
                If setting IsNot Nothing Then
                    ' Update existing setting
                    setting.SettingValue = settingValue
                    setting.LastUpdate = DateTime.Now
                Else
                    ' Create new setting
                    setting = New tm_settings() With {
                        .SettingName = settingName,
                        .SettingValue = settingValue,
                        .Description = description,
                        .LastUpdate = DateTime.Now
                    }
                    dc.tm_settings.Add(setting)
                End If
                
                ' Save changes
                dc.SaveChanges()
                Return True
            End Using
        Catch ex As Exception
            ' Log error
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' Get the configured COM port for the weighbridge
    ''' </summary>
    ''' <returns>COM port name (e.g., "COM1")</returns>
    Public Shared Function GetWeighbridgeComPort() As String
        Return GetSetting("WeighbridgeComPort", "COM1")
    End Function
    
    ''' <summary>
    ''' Get the configured baud rate for the weighbridge
    ''' </summary>
    ''' <returns>Baud rate as integer</returns>
    Public Shared Function GetWeighbridgeBaudRate() As Integer
        Dim baudRateStr = GetSetting("WeighbridgeBaudRate", "9600")
        Dim baudRate As Integer
        
        If Integer.TryParse(baudRateStr, baudRate) Then
            Return baudRate
        Else
            Return 9600 ' Default
        End If
    End Function
    
    ''' <summary>
    ''' Check if dummy mode is enabled for the weighbridge
    ''' </summary>
    ''' <returns>True if dummy mode is enabled</returns>
    Public Shared Function IsDummyModeEnabled() As Boolean
        Dim dummyModeStr = GetSetting("WeighbridgeDummyMode", "False")
        Dim dummyMode As Boolean
        
        If Boolean.TryParse(dummyModeStr, dummyMode) Then
            Return dummyMode
        Else
            Return False ' Default to real hardware
        End If
    End Function
End Class
