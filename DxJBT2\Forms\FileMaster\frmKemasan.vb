Imports System.Data.Entity
Imports DxJBT2.Helpers

Public Class frmKemasan
    Inherits frmBaseList

    Private Shared Shadows ThisForm As frmKemasan

    Public Sub New()
        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        refreshData("")
    End Sub

    Public Shared Function CreateInstance() As frmKemasan
        Dim f As frmKemasan
        ThisForm = Nothing
        If ThisForm Is Nothing Then
            ThisForm = New frmKemasan
        End If
        f = DirectCast(ThisForm, frmKemasan)
        Return f
    End Function

    Public Sub DbAction(msg As String)
        Select Case msg.Trim.ToUpper
            Case "ADD"
                addRecord()
            Case "EDIT"
                editRecord()
            Case "VIEW"
                viewRecord()
            Case "DELETE"
                DeleteRecord()
            Case Else
                Close()
        End Select
    End Sub

    Private Sub editRecord()
        Dim o = CType(TmkemasanBindingSource.Current, tm_kemasan)
        If o Is Nothing Then
            Return
        End If

        frmKemasan_edit.showAs_Edit(New frmKemasan_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub refreshData(ByVal s As String)
        Dim dc As New dxjbt2Entities
        Dim q = From c In dc.tm_kemasan Where c.IsActive = True Order By c.KodeKemasan Select c
        TmkemasanBindingSource.DataSource = q.ToList
    End Sub

    Private Sub addRecord()
        frmKemasan_edit.showAs_Add(New frmKemasan_edit.MsgHandler(AddressOf refreshData))
    End Sub

    Private Sub viewRecord()
        Dim o = CType(TmkemasanBindingSource.Current, tm_kemasan)
        If o Is Nothing Then
            Return
        End If

        frmKemasan_edit.showAs_View(New frmKemasan_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub DeleteRecord()
        Dim o = CType(TmkemasanBindingSource.Current, tm_kemasan)
        If o Is Nothing Then
            Return
        End If

        Dim dr As DialogResult = MessageBox.Show("Are you sure you want to delete this kemasan?" & vbCrLf & _
                                               "Kode: " & o.KodeKemasan & vbCrLf & _
                                               "Nama: " & o.NamaKemasan & vbCrLf & _
                                               "Ukuran: " & o.UkuranKemasan, _
                                               "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

        If dr = DialogResult.Yes Then
            Try
                Dim dc As New dxjbt2Entities
                Dim q = From c In dc.tm_kemasan Where c.Id = o.Id
                Dim kemasan = q.FirstOrDefault
                If kemasan IsNot Nothing Then
                    ' Soft delete - set IsActive to false
                    kemasan.IsActive = False
                    kemasan.ModifiedDate = DateTime.Now
                    kemasan.ModifiedBy = SecurityHelper.GetCurrentUsername()
                    dc.SaveChanges()
                    refreshData("")
                    MessageBox.Show("Kemasan deleted successfully", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            Catch ex As Exception
                MessageBox.Show("Delete Failed: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class
