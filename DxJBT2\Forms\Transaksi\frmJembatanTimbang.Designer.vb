<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmJembatanTimbang
    Inherits frmBaseList

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.GridControl1 = New DevExpress.XtraGrid.GridControl()
        Me.TrjembatantimbangBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.colNoTransaksi = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTanggalMasuk = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colTanggalKeluar = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNoPolisi = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNamaSupir = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colNamaMitra = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colJenisMuatan = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBeratMasuk = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBeratKeluar = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colBeratNetto = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.colStatusTransaksi = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TrjembatantimbangBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblHeader
        '
        Me.lblHeader.Size = New System.Drawing.Size(1181, 54)
        Me.lblHeader.Text = "       Data List Jembatan Timbang"
        '
        'GridControl1
        '
        Me.GridControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridControl1.DataSource = Me.TrjembatantimbangBindingSource
        Me.GridControl1.Location = New System.Drawing.Point(7, 68)
        Me.GridControl1.MainView = Me.GridView1
        Me.GridControl1.Name = "GridControl1"
        Me.GridControl1.Size = New System.Drawing.Size(1167, 409)
        Me.GridControl1.TabIndex = 35
        Me.GridControl1.ViewCollection.AddRange(New DevExpress.XtraGrid.Views.Base.BaseView() {Me.GridView1})
        '
        'TrjembatantimbangBindingSource
        '
        Me.TrjembatantimbangBindingSource.DataSource = GetType(DxJBT2.tr_jembatan_timbang)
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.colNoTransaksi, Me.colTanggalMasuk, Me.colTanggalKeluar, Me.colNoPolisi, Me.colNamaSupir, Me.colNamaMitra, Me.colJenisMuatan, Me.colBeratMasuk, Me.colBeratKeluar, Me.colBeratNetto, Me.colStatusTransaksi})
        Me.GridView1.GridControl = Me.GridControl1
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsBehavior.ReadOnly = True
        '
        'colNoTransaksi
        '
        Me.colNoTransaksi.Caption = "No. Transaksi"
        Me.colNoTransaksi.FieldName = "NoTransaksi"
        Me.colNoTransaksi.MinWidth = 25
        Me.colNoTransaksi.Name = "colNoTransaksi"
        Me.colNoTransaksi.Visible = True
        Me.colNoTransaksi.VisibleIndex = 0
        Me.colNoTransaksi.Width = 120
        '
        'colTanggalMasuk
        '
        Me.colTanggalMasuk.Caption = "Tanggal Masuk"
        Me.colTanggalMasuk.DisplayFormat.FormatString = "dd/MM/yyyy HH:mm"
        Me.colTanggalMasuk.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colTanggalMasuk.FieldName = "TanggalMasuk"
        Me.colTanggalMasuk.MinWidth = 25
        Me.colTanggalMasuk.Name = "colTanggalMasuk"
        Me.colTanggalMasuk.Visible = True
        Me.colTanggalMasuk.VisibleIndex = 1
        Me.colTanggalMasuk.Width = 120
        '
        'colTanggalKeluar
        '
        Me.colTanggalKeluar.Caption = "Tanggal Keluar"
        Me.colTanggalKeluar.DisplayFormat.FormatString = "dd/MM/yyyy HH:mm"
        Me.colTanggalKeluar.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime
        Me.colTanggalKeluar.FieldName = "TanggalKeluar"
        Me.colTanggalKeluar.MinWidth = 25
        Me.colTanggalKeluar.Name = "colTanggalKeluar"
        Me.colTanggalKeluar.Visible = True
        Me.colTanggalKeluar.VisibleIndex = 2
        Me.colTanggalKeluar.Width = 120
        '
        'colNoPolisi
        '
        Me.colNoPolisi.Caption = "No. Polisi"
        Me.colNoPolisi.FieldName = "NoPolisi"
        Me.colNoPolisi.MinWidth = 25
        Me.colNoPolisi.Name = "colNoPolisi"
        Me.colNoPolisi.Visible = True
        Me.colNoPolisi.VisibleIndex = 3
        Me.colNoPolisi.Width = 100
        '
        'colNamaSupir
        '
        Me.colNamaSupir.Caption = "Nama Supir"
        Me.colNamaSupir.FieldName = "NamaSupir"
        Me.colNamaSupir.MinWidth = 25
        Me.colNamaSupir.Name = "colNamaSupir"
        Me.colNamaSupir.Visible = True
        Me.colNamaSupir.VisibleIndex = 4
        Me.colNamaSupir.Width = 120
        '
        'colNamaMitra
        '
        Me.colNamaMitra.Caption = "Nama Mitra"
        Me.colNamaMitra.FieldName = "NamaMitra"
        Me.colNamaMitra.MinWidth = 25
        Me.colNamaMitra.Name = "colNamaMitra"
        Me.colNamaMitra.Visible = True
        Me.colNamaMitra.VisibleIndex = 5
        Me.colNamaMitra.Width = 150
        '
        'colJenisMuatan
        '
        Me.colJenisMuatan.Caption = "Jenis Muatan"
        Me.colJenisMuatan.FieldName = "JenisMuatan"
        Me.colJenisMuatan.MinWidth = 25
        Me.colJenisMuatan.Name = "colJenisMuatan"
        Me.colJenisMuatan.Visible = True
        Me.colJenisMuatan.VisibleIndex = 6
        Me.colJenisMuatan.Width = 120
        '
        'colBeratMasuk
        '
        Me.colBeratMasuk.Caption = "Berat Masuk (kg)"
        Me.colBeratMasuk.DisplayFormat.FormatString = "#,##0.00"
        Me.colBeratMasuk.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colBeratMasuk.FieldName = "BeratMasuk"
        Me.colBeratMasuk.MinWidth = 25
        Me.colBeratMasuk.Name = "colBeratMasuk"
        Me.colBeratMasuk.Visible = True
        Me.colBeratMasuk.VisibleIndex = 7
        Me.colBeratMasuk.Width = 100
        '
        'colBeratKeluar
        '
        Me.colBeratKeluar.Caption = "Berat Keluar (kg)"
        Me.colBeratKeluar.DisplayFormat.FormatString = "#,##0.00"
        Me.colBeratKeluar.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colBeratKeluar.FieldName = "BeratKeluar"
        Me.colBeratKeluar.MinWidth = 25
        Me.colBeratKeluar.Name = "colBeratKeluar"
        Me.colBeratKeluar.Visible = True
        Me.colBeratKeluar.VisibleIndex = 8
        Me.colBeratKeluar.Width = 100
        '
        'colBeratNetto
        '
        Me.colBeratNetto.Caption = "Berat Netto (kg)"
        Me.colBeratNetto.DisplayFormat.FormatString = "#,##0.00"
        Me.colBeratNetto.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.colBeratNetto.FieldName = "BeratNetto"
        Me.colBeratNetto.MinWidth = 25
        Me.colBeratNetto.Name = "colBeratNetto"
        Me.colBeratNetto.Visible = True
        Me.colBeratNetto.VisibleIndex = 9
        Me.colBeratNetto.Width = 100
        '
        'colStatusTransaksi
        '
        Me.colStatusTransaksi.Caption = "Status"
        Me.colStatusTransaksi.FieldName = "StatusTransaksi"
        Me.colStatusTransaksi.MinWidth = 25
        Me.colStatusTransaksi.Name = "colStatusTransaksi"
        Me.colStatusTransaksi.Visible = True
        Me.colStatusTransaksi.VisibleIndex = 10
        Me.colStatusTransaksi.Width = 80
        '
        'frmJembatanTimbang
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1180, 502)
        Me.Controls.Add(Me.GridControl1)
        Me.Name = "frmJembatanTimbang"
        Me.Text = "Jembatan Timbang"
        Me.Controls.SetChildIndex(Me.lblHeader, 0)
        Me.Controls.SetChildIndex(Me.GridControl1, 0)
        CType(Me.GridControl1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TrjembatantimbangBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents GridControl1 As DevExpress.XtraGrid.GridControl
    Friend WithEvents TrjembatantimbangBindingSource As BindingSource
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents colNoTransaksi As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTanggalMasuk As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colTanggalKeluar As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNoPolisi As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNamaSupir As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colNamaMitra As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colJenisMuatan As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBeratMasuk As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBeratKeluar As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colBeratNetto As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents colStatusTransaksi As DevExpress.XtraGrid.Columns.GridColumn
End Class
