# Script untuk membuat ikon security dengan simbol gembok/kunci
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

# Fungsi untuk membuat gambar gembok sebagai simbol security
function New-SecurityIcon {
    param (
        [string]$outputPath,
        [System.Drawing.Color]$backgroundColor,
        [System.Drawing.Color]$lockColor,
        [int]$size = 16
    )
    
    try {
        # Buat bitmap dengan ukuran yang ditentukan
        $bitmap = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Aktifkan anti-aliasing untuk gambar yang lebih halus
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        
        # Isi dengan warna latar belakang
        $graphics.Clear($backgroundColor)
        
        # Buat brush dan pen untuk warna gembok
        $lockBrush = New-Object System.Drawing.SolidBrush($lockColor)
        $lockPen = New-Object System.Drawing.Pen($lockColor, 1)
        
        # Hitung proporsi untuk ukuran yang berbeda
        $scale = $size / 16.0
        
        # Gambar badan gembok (persegi panjang dengan sudut membulat)
        $lockBodyX = [int](4 * $scale)
        $lockBodyY = [int](7 * $scale)
        $lockBodyWidth = [int](8 * $scale)
        $lockBodyHeight = [int](7 * $scale)
        $cornerRadius = [int](2 * $scale)
        
        # Buat path untuk gembok dengan sudut membulat
        $path = New-Object System.Drawing.Drawing2D.GraphicsPath
        
        # Tambahkan garis dan kurva untuk membuat persegi panjang dengan sudut membulat
        $path.AddArc($lockBodyX, $lockBodyY, $cornerRadius * 2, $cornerRadius * 2, 180, 90)
        $path.AddArc($lockBodyX + $lockBodyWidth - $cornerRadius * 2, $lockBodyY, $cornerRadius * 2, $cornerRadius * 2, 270, 90)
        $path.AddArc($lockBodyX + $lockBodyWidth - $cornerRadius * 2, $lockBodyY + $lockBodyHeight - $cornerRadius * 2, $cornerRadius * 2, $cornerRadius * 2, 0, 90)
        $path.AddArc($lockBodyX, $lockBodyY + $lockBodyHeight - $cornerRadius * 2, $cornerRadius * 2, $cornerRadius * 2, 90, 90)
        $path.CloseFigure()
        
        # Isi dan gambar path
        $graphics.FillPath($lockBrush, $path)
        
        # Gambar lengkungan gembok di atas
        $shackleX1 = [int](5 * $scale)
        $shackleY1 = [int](7 * $scale)
        $shackleX2 = [int](11 * $scale)
        $shackleY2 = [int](7 * $scale)
        $shackleTopY = [int](3 * $scale)
        
        # Gambar lengkungan dengan garis
        $lockPen.Width = [int](1.5 * $scale)
        $graphics.DrawLine($lockPen, $shackleX1, $shackleY1, $shackleX1, $shackleTopY)
        $graphics.DrawLine($lockPen, $shackleX1, $shackleTopY, $shackleX2, $shackleTopY)
        $graphics.DrawLine($lockPen, $shackleX2, $shackleTopY, $shackleX2, $shackleY2)
        
        # Gambar lubang kunci
        $keyHoleX = [int](8 * $scale)
        $keyHoleY = [int](10 * $scale)
        $keyHoleSize = [int](2 * $scale)
        $keyHoleRect = New-Object System.Drawing.Rectangle($keyHoleX, $keyHoleY, $keyHoleSize, $keyHoleSize)
        $keyHoleBrush = New-Object System.Drawing.SolidBrush($backgroundColor)
        $graphics.FillEllipse($keyHoleBrush, $keyHoleRect)
        $keyHoleBrush.Dispose()
        
        # Simpan gambar
        $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Bersihkan resources
        $path.Dispose()
        $lockPen.Dispose()
        $lockBrush.Dispose()
        $graphics.Dispose()
        $bitmap.Dispose()
        
        Write-Host "Gambar security berhasil dibuat: $outputPath"
    }
    catch {
        Write-Host "Error saat membuat gambar security: $_"
    }
}

# Buat direktori Resources jika belum ada
$resourcesDir = "d:\SourceCode\Bright\DX24_1\DxJBT2\DxJBT2\Resources"
if (-not (Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir
}

# Buat ikon untuk menu Security dengan ukuran 16x16
$securityPath = Join-Path $resourcesDir "Security_16x16.png"
New-SecurityIcon -outputPath $securityPath -backgroundColor ([System.Drawing.Color]::FromArgb(192, 57, 43)) -lockColor ([System.Drawing.Color]::White) -size 16

Write-Host "Pembuatan ikon security selesai."
