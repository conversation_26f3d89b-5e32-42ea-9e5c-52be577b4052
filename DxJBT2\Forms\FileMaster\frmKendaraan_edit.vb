Imports DxJBT2

Public Class frmKendaraan_edit
    Inherits frmEntryBase

    ' Public Delegate Sub MsgHandler(ByVal msg As String)
    Private Shared ThisForm As frmKendaraan_edit

    Public Sub New()
        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
    End Sub

    Public Shared Sub showAs_Add(ByVal callBack As MsgHandler)
        ThisForm = New frmKendaraan_edit
        ThisForm.callBack = callBack
        ThisForm.dbAction = "add"
        ThisForm.TmkendaraanBindingSource.AddNew()
        ThisForm.ShowDialog()
    End Sub

    Public Shared Sub showAs_Edit(ByVal callBack As MsgHandler, ByVal id As Integer)
        Dim f As frmKendaraan_edit = New frmKendaraan_edit
        With f
            .callBack = callBack
            .dbAction = "edit"

            Dim dc As New dxjbt2Entities
            .TmkendaraanBindingSource.DataSource = (From c In dc.tm_kendaraan Where c.Id = id Select c).ToList

            .ShowDialog()
        End With
    End Sub

    Public Shared Sub showAs_View(ByVal callBack As MsgHandler, ByVal id As Integer)
        Dim f As frmKendaraan_edit = New frmKendaraan_edit
        With f
            .callBack = callBack
            .dbAction = "view"

            Dim dc As New dxjbt2Entities
            .TmkendaraanBindingSource.DataSource = (From c In dc.tm_kendaraan Where c.Id = id Select c).ToList

            ' Disable editing in view mode
            .NoPolisiTextEdit.ReadOnly = True
            .NamaSupirTextEdit.ReadOnly = True
            .TipeKendaraan_idSearchLookUpEdit.ReadOnly = True
            .Mitra_idSearchLookUpEdit.ReadOnly = True

            ' Change button visibility/text
            .btnSave.Visible = False
            .btnCancel.Text = "Close"

            .ShowDialog()
        End With
    End Sub

    Private callBack As MsgHandler
    Private dbAction As String = "add"

    Private Sub loadData(ByVal id As Integer)
        Dim dc As New dxjbt2Entities
        Dim q = From c In dc.tm_kendaraan Where c.Id = id Select c
        If q.Count > 0 Then
            TmkendaraanBindingSource.DataSource = q.ToList
        End If
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If Not validateData() Then
            Return
        End If

        Try
            Dim o As tm_kendaraan = CType(TmkendaraanBindingSource.Current, tm_kendaraan)

            ' Remove all spaces from NoPolisi
            o.NoPolisi = o.NoPolisi.Replace(" ", "")

            Dim dc As New dxjbt2Entities

            If o.Id = 0 Then
                ' For new records, add directly
                dc.tm_kendaraan.Add(o)
            Else
                ' For existing records, create a new entity and copy properties
                ' This avoids the multiple tracking issue
                Dim existingEntity = dc.tm_kendaraan.Find(o.Id)
                If existingEntity IsNot Nothing Then
                    ' Copy properties from the form entity to the database entity
                    existingEntity.NoPolisi = o.NoPolisi
                    existingEntity.TipeKendaraan_id = o.TipeKendaraan_id
                    existingEntity.Mitra_id = o.Mitra_id
                    existingEntity.NamaSupir = o.NamaSupir

                    ' Entity is already being tracked by the context
                    dc.Entry(existingEntity).State = System.Data.Entity.EntityState.Modified
                End If
            End If

            dc.SaveChanges()
            callBack("")
            isClosing = True
            Close()
        Catch ex As System.Data.Entity.Validation.DbEntityValidationException
            ' Handle validation errors
            Dim errorMessages As String = ""
            For Each validationErrors In ex.EntityValidationErrors
                For Each validationError In validationErrors.ValidationErrors
                    errorMessages &= String.Format("Property: {0} Error: {1}", validationError.PropertyName, validationError.ErrorMessage) & vbCrLf
                Next
            Next
            MessageBox.Show("Validation failed: " & vbCrLf & errorMessages, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Catch ex As System.Data.Entity.Infrastructure.DbUpdateException
            ' Handle database update errors (e.g., duplicate key, foreign key violations)
            If ex.InnerException IsNot Nothing AndAlso ex.InnerException.InnerException IsNot Nothing Then
                MessageBox.Show("Database error: " & ex.InnerException.InnerException.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Else
                MessageBox.Show("Database error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            ' Handle any other errors
            MessageBox.Show("An error occurred while saving: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function validateData() As Boolean
        If Not DxValidationProvider1.Validate Then
            Return False
        End If
        Dim o As tm_kendaraan = CType(TmkendaraanBindingSource.Current, tm_kendaraan)

        If String.IsNullOrEmpty(o.NoPolisi) Then
            MessageBox.Show("Nomor Polisi tidak boleh kosong", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If o.TipeKendaraan_id = 0 Then
            MessageBox.Show("Tipe Kendaraan harus dipilih", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If o.Mitra_id = 0 Then
            MessageBox.Show("Mitra harus dipilih", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If String.IsNullOrEmpty(o.NamaSupir) Then
            MessageBox.Show("Nama Supir tidak boleh kosong", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Return True
    End Function

    Private Sub frmKendaraan_edit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Load lookup data for Tipe Kendaraan
        Dim dc As New dxjbt2Entities

        ' Setup Tipe Kendaraan lookup
        Dim tipeList = From c In dc.tm_tipe_kendaraan Order By c.NamaTipe Select c
        TipeKendaraan_idSearchLookUpEdit.Properties.DataSource = tipeList.ToList
        TipeKendaraan_idSearchLookUpEdit.Properties.DisplayMember = "NamaTipe"
        TipeKendaraan_idSearchLookUpEdit.Properties.ValueMember = "Id"

        ' Setup Mitra lookup
        Dim mitraList = From c In dc.tm_mitra Order By c.NamaMitra Select c
        Mitra_idSearchLookUpEdit.Properties.DataSource = mitraList.ToList
        Mitra_idSearchLookUpEdit.Properties.DisplayMember = "NamaMitra"
        Mitra_idSearchLookUpEdit.Properties.ValueMember = "Id"
    End Sub

    Private Sub NoPolisiTextEdit_KeyPress(sender As Object, e As KeyPressEventArgs) Handles NoPolisiTextEdit.KeyPress
        e.KeyChar = e.KeyChar.ToString.ToUpper
    End Sub

    Private Sub frmKendaraan_edit_Activated(sender As Object, e As EventArgs) Handles Me.Activated
        NoPolisiTextEdit.Focus()
    End Sub
End Class