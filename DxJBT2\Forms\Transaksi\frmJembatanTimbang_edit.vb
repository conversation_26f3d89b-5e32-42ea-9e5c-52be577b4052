Imports DxJBT2
Imports System.IO.Ports
Imports DxJBT2.SettingsHelper

Public Class frmJembatanTimbang_edit
    Inherits frmEntryBase

    Public Delegate Sub MsgHandler(ByVal s As String)
    Private modRefreshData As MsgHandler
    Private dbAction As String

    ' Serial port for weighbridge communication
    Private WithEvents serialPort As SerialPort
    Private weightBuffer As String = ""

    ' Timer for dummy mode to simulate weight readings
    Private WithEvents dummyTimer As Timer

    ' Flag to indicate if dummy mode is enabled
    Private dummyModeEnabled As Boolean = False

    Friend Shared Sub showAs_Add(msg As MsgHandler)
        Dim f = New frmJembatanTimbang_edit
        With f
            .modRefreshData = msg
            .dbAction = "add"

            Dim dc As New dxjbt2Entities

            .TrjembatantimbangBindingSource.DataSource = (From c In dc.tr_jembatan_timbang Where c.Id = 0 Select c).ToList
            .TrjembatantimbangBindingSource.AddNew()

            ' Set default values
            Dim o = CType(.TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
            o.Tang<PERSON>uk = DateTime.Now
            o.StatusTransaksi = "OPEN"
            o.NoTransaksi = f.GenerateTransactionNumber()

            ' Load lookup data
            f.LoadLookupData()

            ' Make No Transaksi and date fields read-only
            f.NoTransaksiTextEdit.ReadOnly = True
            f.TanggalMasukDateEdit.ReadOnly = True

            .ShowDialog()
        End With
    End Sub

    Private Function GenerateTransactionNumber() As String
        ' Generate transaction number with format JT-yyyyMMdd-nnn
        Dim dc As New dxjbt2Entities
        Dim today = DateTime.Now.ToString("yyyyMMdd")
        Dim prefix = "JT-" & today & "-"

        ' Find the last transaction number for today
        Dim lastTrans = (From c In dc.tr_jembatan_timbang
                         Where c.NoTransaksi.StartsWith(prefix)
                         Order By c.NoTransaksi Descending
                         Select c.NoTransaksi).FirstOrDefault()

        Dim newNumber As Integer = 1
        If Not String.IsNullOrEmpty(lastTrans) Then
            Dim lastNumber = lastTrans.Substring(prefix.Length)
            Integer.TryParse(lastNumber, newNumber)
            newNumber += 1
        End If

        Return prefix & newNumber.ToString("000")
    End Function

    Private Sub LoadLookupData()
        ' Set date format to dd MMM yyyy
        TanggalMasukDateEdit.Properties.DisplayFormat.FormatString = "dd MMM yyyy"
        TanggalMasukDateEdit.Properties.EditFormat.FormatString = "dd MMM yyyy"
        TanggalKeluarDateEdit.Properties.DisplayFormat.FormatString = "dd MMM yyyy"
        TanggalKeluarDateEdit.Properties.EditFormat.FormatString = "dd MMM yyyy"

        ' Load kendaraan data
        LoadKendaraanData()

        ' Make date fields read-only
        TanggalMasukDateEdit.ReadOnly = True
        TanggalKeluarDateEdit.ReadOnly = True

        ' Make NamaSupir and Mitra fields read-only
        NamaSupirTextEdit.ReadOnly = True
        Mitra_idSearchLookUpEdit.ReadOnly = True

        ' Make weight fields read-only
        BeratMasukTextEdit.ReadOnly = True
        BeratKeluarTextEdit.ReadOnly = True
        BeratNettoTextEdit.ReadOnly = True
        StatusTransaksiTextEdit.ReadOnly = True



        ' Configure Kendaraan grid view columns
        With SearchLookUpEdit1View
            .Columns.Clear()
            .Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn With {
                .FieldName = "NoPolisi",
                .Caption = "No Polisi",
                .Visible = True,
                .VisibleIndex = 0
            })
            .Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn With {
                .FieldName = "tm_tipe_kendaraan.NamaTipe",
                .Caption = "Tipe Kendaraan",
                .Visible = True,
                .VisibleIndex = 1
            })
        End With

        ' Load Mitra data
        Dim dc As New dxjbt2Entities
        Dim mitraData = From m In dc.tm_mitra Select m
        Mitra_idSearchLookUpEdit.Properties.DataSource = mitraData.ToList()
        Mitra_idSearchLookUpEdit.Properties.DisplayMember = "NamaMitra"
        Mitra_idSearchLookUpEdit.Properties.ValueMember = "Id"

        ' Configure Mitra grid view columns
        With GridView1
            .Columns.Clear()
            .Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn With {
                .FieldName = "KodeMitra",
                .Caption = "Kode",
                .Visible = True,
                .VisibleIndex = 0
            })
            .Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn With {
                .FieldName = "NamaMitra",
                .Caption = "Nama Mitra",
                .Visible = True,
                .VisibleIndex = 1
            })
            .Columns.Add(New DevExpress.XtraGrid.Columns.GridColumn With {
                .FieldName = "TipeMitra",
                .Caption = "Tipe",
                .Visible = True,
                .VisibleIndex = 2
            })
        End With
    End Sub

    Private Sub LoadKendaraanData()
        ' Load Kendaraan data for lookup
        Dim dc As New dxjbt2Entities

        ' Load Kendaraan data
        Dim kendaraanData = From k In dc.tm_kendaraan Select k
        Kendaraan_idSearchLookUpEdit.Properties.DataSource = kendaraanData.ToList()
        Kendaraan_idSearchLookUpEdit.Properties.DisplayMember = "NoPolisi"
        Kendaraan_idSearchLookUpEdit.Properties.ValueMember = "Id"
    End Sub

    Private Sub btnAddKendaraan_Click(sender As Object, e As EventArgs) Handles btnAddKendaraan.Click
        ' Open form to add new kendaraan
        Try
            ' Create callback to refresh kendaraan data after adding new vehicle
            Dim refreshCallback As New frmEntryBase.MsgHandler(AddressOf RefreshKendaraanData)

            ' Show add kendaraan form
            frmKendaraan_edit.showAs_Add(refreshCallback)

        Catch ex As Exception
            MessageBox.Show($"Error opening add kendaraan form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub RefreshKendaraanData(msg As String)
        ' Refresh kendaraan data after adding new vehicle
        LoadKendaraanData()
    End Sub

    Private Sub btnKemasan_Click(sender As Object, e As EventArgs) Handles btnKemasan.Click
        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
        If o Is Nothing Then
            MessageBox.Show("Tidak ada data transaksi yang dipilih!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' Jika transaksi belum disimpan, simpan dulu
        If o.Id = 0 Then
            MessageBox.Show("Simpan transaksi terlebih dahulu sebelum menambah kemasan!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            Dim frmKemasan As New frmKemasanDetail()
            frmKemasan.LoadData(o.Id)

            If frmKemasan.ShowDialog() = DialogResult.OK Then
                ' Update total berat kemasan di transaksi
                UpdateTotalBeratKemasan(o.Id)
                UpdateInfoKemasan(o.Id)
            End If
        Catch ex As Exception
            MessageBox.Show("Error opening kemasan detail: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateTotalBeratKemasan(jembatanTimbangId As Integer)
        Try
            Using dc As New dxjbt2Entities
                Dim transaction = dc.tr_jembatan_timbang.Find(jembatanTimbangId)
                If transaction IsNot Nothing Then
                    ' Hitung total berat kemasan dari detail
                    Dim totalBeratKemasan = dc.tr_kemasan_detail _
                                            .Where(Function(k) k.JembatanTimbang_id = jembatanTimbangId) _
                                            .Sum(Function(k) If(k.TotalBeratKemasan, 0))

                    transaction.TotalBeratKemasan = totalBeratKemasan

                    ' Hitung berat netto murni jika berat netto sudah ada
                    If transaction.BeratNetto.HasValue Then
                        transaction.BeratNettoMurni = transaction.BeratNetto.Value - totalBeratKemasan
                    End If

                    dc.SaveChanges()

                    ' Update binding source
                    Dim currentItem = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
                    If currentItem IsNot Nothing AndAlso currentItem.Id = jembatanTimbangId Then
                        currentItem.TotalBeratKemasan = totalBeratKemasan
                        currentItem.BeratNettoMurni = transaction.BeratNettoMurni
                        TrjembatantimbangBindingSource.ResetCurrentItem()
                    End If
                End If
            End Using
        Catch ex As Exception
            MessageBox.Show("Error updating total berat kemasan: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub UpdateInfoKemasan(jembatanTimbangId As Integer)
        Try
            Using dc As New dxjbt2Entities
                Dim kemasanDetails = dc.tr_kemasan_detail.Include("tm_kemasan") _
                                    .Where(Function(k) k.JembatanTimbang_id = jembatanTimbangId) _
                                    .ToList()

                If kemasanDetails.Count > 0 Then
                    Dim totalQty = kemasanDetails.Sum(Function(k) k.Qty)
                    Dim totalBerat = kemasanDetails.Sum(Function(k) k.TotalBeratKemasan)
                    Dim jenisKemasan = kemasanDetails.Count

                    lblInfoKemasan.Text = $"{totalQty} kemasan ({jenisKemasan} jenis) - Total: {totalBerat:N3} kg"
                Else
                    lblInfoKemasan.Text = "Tidak ada kemasan"
                End If
            End Using
        Catch ex As Exception
            lblInfoKemasan.Text = "Error loading kemasan info"
        End Try
    End Sub

    Private Sub ShowPrintSlipDialog(slipData As WeighingSlipData)
        ' Show dialog asking if user wants to print weighing slip
        Try
            Dim result As DialogResult = MessageBox.Show(
                "Penimbangan selesai!" & vbCrLf & vbCrLf &
                "Apakah Anda ingin mencetak slip timbangan?" & vbCrLf &
                "No. Transaksi: " & slipData.NoTransaksi & vbCrLf &
                "No. Polisi: " & slipData.NoPolisi & vbCrLf &
                "Berat Netto: " & slipData.BeratNetto.Value.ToString("#,##0") & " kg" & vbCrLf & vbCrLf &
                "Klik Yes untuk preview, No untuk print langsung, Cancel untuk tidak print",
                "Print Slip Timbangan",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question)

            If result = DialogResult.Yes Then
                ' Create and show print preview
                Dim slipReport As New WeighingSlipReport(slipData)
                slipReport.PrintSlip() ' This will show print preview
            ElseIf result = DialogResult.No Then
                ' Print directly without preview
                Dim slipReport As New WeighingSlipReport(slipData)
                slipReport.PrintDirectly()
                MessageBox.Show("Slip timbangan telah dicetak!", "Print Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            MessageBox.Show("Error saat menampilkan dialog print: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub frmJembatanTimbang_edit_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        LoadLookupData()

        ' Initialize serial port for weighbridge
        InitializeSerialPort()

        ' Update info kemasan untuk record yang sudah ada
        If dbAction <> "add" Then
            Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
            If o IsNot Nothing AndAlso o.Id > 0 Then
                UpdateInfoKemasan(o.Id)
            End If
        End If
    End Sub

    Private Sub frmJembatanTimbang_edit_Activated(sender As Object, e As EventArgs) Handles Me.Activated
        NoTransaksiTextEdit.Focus()
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        ' Validate data and save
        If DxValidationProvider1.Validate = False Then
            Return
        End If

        If TrjembatantimbangBindingSource.Current Is Nothing Then Return

        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)

        ' Validate weights based on transaction type
        If dbAction = "add" Then
            ' For entry weighing, validate BeratMasuk
            If o.BeratMasuk <= 0 Then
                MessageBox.Show("Berat Masuk harus lebih besar dari 0 untuk penimbangan masuk", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                BeratMasukTextEdit.Focus()
                Return
            End If
        Else
            ' For exit weighing, validate BeratKeluar
            If o.BeratKeluar <= 0 Then
                MessageBox.Show("Berat Keluar harus lebih besar dari 0 untuk penimbangan keluar", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                BeratKeluarTextEdit.Focus()
                Return
            End If
        End If

        ' If both entry and exit weights are recorded, update status to CLOSED
        If o.BeratMasuk > 0 AndAlso o.BeratKeluar > 0 Then
            If o.StatusTransaksi = "OPEN" Then
                o.StatusTransaksi = "CLOSED"
            End If
        End If

        If dbAction = "add" Then
            saveAdd()
        Else
            saveEdit()
        End If
        Close()
    End Sub

    Private Sub saveEdit()
        Try
            ' Get the current entity from binding source
            Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)

            ' Validate required fields before proceeding
            If String.IsNullOrEmpty(o.NoTransaksi) Then
                MessageBox.Show("Nomor Transaksi tidak boleh kosong", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                NoTransaksiTextEdit.Focus()
                Return
            End If

            If o.TanggalMasuk = Nothing Then
                MessageBox.Show("Tanggal Masuk tidak boleh kosong", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                TanggalMasukDateEdit.Focus()
                Return
            End If

            ' Validate exit weight for exit weighing
            If o.BeratKeluar <= 0 OrElse o.BeratKeluar Is Nothing Then
                MessageBox.Show("Berat Keluar harus lebih besar dari 0 untuk penimbangan keluar", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                BeratKeluarTextEdit.Focus()
                Return
            End If

            ' Create a new context for this operation
            Using dc As New dxjbt2Entities
                ' Retrieve mitra code, mitra name, and driver name from tm_kendaraan
                If o.Kendaraan_id > 0 Then
                    Dim kendaraan = (From k In dc.tm_kendaraan Where k.Id = o.Kendaraan_id Select k).FirstOrDefault()
                    If kendaraan IsNot Nothing Then
                        ' Set driver name from kendaraan
                        If Not String.IsNullOrEmpty(kendaraan.NamaSupir) Then
                            o.NamaSupir = kendaraan.NamaSupir
                        End If

                        ' Set mitra from kendaraan if available
                        If kendaraan.Mitra_id > 0 Then
                            o.Mitra_id = kendaraan.Mitra_id

                            ' Get mitra details
                            Dim mitra = (From m In dc.tm_mitra Where m.Id = kendaraan.Mitra_id Select m).FirstOrDefault()
                            If mitra IsNot Nothing Then
                                ' Store mitra code and name in appropriate fields if they exist
                                o.KodeMitra = mitra.KodeMitra
                                o.NamaMitra = mitra.NamaMitra
                            End If
                        End If
                    End If
                End If

                ' Find the entity to update
                Dim entity = (From c In dc.tr_jembatan_timbang Where c.Id = o.Id).FirstOrDefault()

                If entity IsNot Nothing Then
                    ' Validate TanggalMasuk is within SQL Server datetime range (1753-01-01 to 9999-12-31)
                    ' TanggalMasuk is not nullable
                    Dim minSqlDate As New DateTime(1753, 1, 1)
                    Dim maxSqlDate As New DateTime(9999, 12, 31)

                    Dim validTanggalMasuk As DateTime
                    If o.TanggalMasuk < minSqlDate Then
                        validTanggalMasuk = minSqlDate
                    ElseIf o.TanggalMasuk > maxSqlDate Then
                        validTanggalMasuk = maxSqlDate
                    Else
                        validTanggalMasuk = o.TanggalMasuk
                    End If

                    ' Validate TanggalKeluar if it has a value
                    Dim validTanggalKeluar As DateTime? = Nothing
                    If o.TanggalKeluar.HasValue Then
                        If o.TanggalKeluar.Value < minSqlDate Then
                            validTanggalKeluar = minSqlDate
                        ElseIf o.TanggalKeluar.Value > maxSqlDate Then
                            validTanggalKeluar = maxSqlDate
                        Else
                            validTanggalKeluar = o.TanggalKeluar
                        End If
                    End If

                    ' Update all properties with validated values
                    entity.NoTransaksi = o.NoTransaksi
                    entity.TanggalMasuk = validTanggalMasuk
                    entity.TanggalKeluar = validTanggalKeluar

                    ' Set LastUpdate to current date and time
                    entity.LastUpdate = DateTime.Now
                    entity.Kendaraan_id = o.Kendaraan_id
                    entity.NoPolisi = o.NoPolisi
                    entity.NamaSupir = o.NamaSupir
                    entity.Mitra_id = o.Mitra_id
                    entity.KodeMitra = o.KodeMitra
                    entity.NamaMitra = o.NamaMitra
                    entity.AsalMuatan = o.AsalMuatan
                    entity.TujuanMuatan = o.TujuanMuatan
                    entity.BeratMasuk = o.BeratMasuk
                    entity.BeratKeluar = o.BeratKeluar
                    entity.BeratNetto = o.BeratNetto
                    entity.StatusTransaksi = o.StatusTransaksi
                    entity.NoBukti = o.NoBukti
                    entity.Keterangan = o.Keterangan

                    ' Save changes
                    dc.SaveChanges()
                Else
                    MessageBox.Show("Data tidak ditemukan. Mungkin telah dihapus oleh pengguna lain.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If
            End Using
            modRefreshData(o.Id.ToString)

            ' Check if this is a completed weighing (both entry and exit weights recorded)
            If o.BeratMasuk > 0 AndAlso o.BeratKeluar.HasValue AndAlso o.BeratKeluar.Value > 0 AndAlso o.StatusTransaksi = "CLOSED" Then
                ' Load complete data with related entities for printing
                Using printDc As New dxjbt2Entities
                    Dim completeTransaction = (From t In printDc.tr_jembatan_timbang.Include("tm_kendaraan").Include("tm_mitra").Include("tr_kemasan_detail")
                                              Where t.Id = o.Id
                                              Select t).FirstOrDefault()

                    If completeTransaction IsNot Nothing Then
                        ' Create slip data DTO to avoid context disposal issues
                        Dim slipData = WeighingSlipReport.CreateSlipData(completeTransaction)
                        ShowPrintSlipDialog(slipData)
                    End If
                End Using
            End If

            MessageBox.Show("Successfully Updated!", "Update", MessageBoxButtons.OK, MessageBoxIcon.Information)
            isClosing = True
        Catch ex As System.Data.Entity.Validation.DbEntityValidationException
            ' Handle Entity Framework validation errors
            Dim errorMessages As New System.Text.StringBuilder("Update gagal! Terdapat kesalahan validasi:" & vbCrLf & vbCrLf)

            ' Loop through each validation error
            For Each validationError In ex.EntityValidationErrors
                For Each _err In validationError.ValidationErrors
                    ' Translate property names to user-friendly field names
                    Dim fieldName As String = _err.PropertyName
                    Select Case _err.PropertyName
                        Case "NoTransaksi"
                            fieldName = "Nomor Transaksi"
                        Case "TanggalMasuk"
                            fieldName = "Tanggal Masuk"
                        Case "Kendaraan_id"
                            fieldName = "Kendaraan"
                        Case "NoPolisi"
                            fieldName = "Nomor Polisi"
                        Case "NamaSupir"
                            fieldName = "Nama Supir"
                        Case "Mitra_id"
                            fieldName = "Mitra"
                        Case "BeratMasuk"
                            fieldName = "Berat Masuk"
                        Case "BeratKeluar"
                            fieldName = "Berat Keluar"
                        Case "StatusTransaksi"
                            fieldName = "Status Transaksi"
                    End Select

                    errorMessages.AppendLine("- Field '" & fieldName & "': " & _err.ErrorMessage)
                Next
            Next

            MessageBox.Show(errorMessages.ToString(), "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)

        Catch ex As Exception
            ' Handle specific validation exceptions with more detailed messages
            If ex.Message.Contains("not be null") Or ex.Message.Contains("NULL") Then
                ' Extract field name from error message if possible
                Dim fieldName As String = "Unknown field"
                If ex.Message.Contains("NoTransaksi") Then
                    fieldName = "Nomor Transaksi"
                ElseIf ex.Message.Contains("TanggalMasuk") Then
                    fieldName = "Tanggal Masuk"
                ElseIf ex.Message.Contains("Kendaraan_id") Then
                    fieldName = "Kendaraan"
                ElseIf ex.Message.Contains("NoPolisi") Then
                    fieldName = "Nomor Polisi"
                ElseIf ex.Message.Contains("NamaSupir") Then
                    fieldName = "Nama Supir"
                ElseIf ex.Message.Contains("Mitra_id") Then
                    fieldName = "Mitra"
                ElseIf ex.Message.Contains("BeratMasuk") Then
                    fieldName = "Berat Masuk"
                ElseIf ex.Message.Contains("BeratKeluar") Then
                    fieldName = "Berat Keluar"
                ElseIf ex.Message.Contains("StatusTransaksi") Then
                    fieldName = "Status Transaksi"
                End If

                MessageBox.Show("Update gagal! Field " & fieldName & " tidak boleh kosong." & vbCrLf & vbCrLf &
                              "Detail error: " & ex.Message, "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            ElseIf ex.Message.Contains("duplicate") Or ex.Message.Contains("UNIQUE") Then
                ' Handle duplicate key errors
                MessageBox.Show("Update gagal! Data dengan nomor transaksi ini sudah ada dalam database." & vbCrLf & vbCrLf &
                              "Detail error: " & ex.Message, "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            ElseIf ex.Message.Contains("foreign key") Or ex.Message.Contains("FOREIGN KEY") Then
                ' Handle foreign key errors
                MessageBox.Show("Update gagal! Referensi ke data master (Kendaraan atau Mitra) tidak valid." & vbCrLf & vbCrLf &
                              "Detail error: " & ex.Message, "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Else
                ' General error message for other exceptions
                MessageBox.Show("Update gagal! " & vbCrLf & vbCrLf & "Detail error: " & ex.Message,
                               "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        End Try
    End Sub

    Friend Shared Sub showAs_Edit(msg As MsgHandler, id As Integer)
        Dim f = New frmJembatanTimbang_edit
        With f
            .modRefreshData = msg
            .dbAction = "edit"

            Dim dc As New dxjbt2Entities
            .TrjembatantimbangBindingSource.DataSource = (From c In dc.tr_jembatan_timbang Where c.Id = id Select c).ToList

            ' Load lookup data
            f.LoadLookupData()

            ' Make No Transaksi and date fields read-only
            f.NoTransaksiTextEdit.ReadOnly = True
            f.TanggalMasukDateEdit.ReadOnly = True

            .btnSave.Text = "Update"
            .btnCancel.Text = "Close"
            .ShowDialog()
        End With
    End Sub

    Friend Shared Sub showAs_View(msg As MsgHandler, id As Integer)
        Dim f = New frmJembatanTimbang_edit
        With f
            .modRefreshData = msg
            .dbAction = "view"

            Dim dc As New dxjbt2Entities
            .TrjembatantimbangBindingSource.DataSource = (From c In dc.tr_jembatan_timbang Where c.Id = id Select c).ToList

            ' Load lookup data
            f.LoadLookupData()

            ' Disable all edit controls
            .NoTransaksiTextEdit.ReadOnly = True
            .TanggalMasukDateEdit.ReadOnly = True
            .TanggalKeluarDateEdit.ReadOnly = True
            .Kendaraan_idSearchLookUpEdit.ReadOnly = True
            .NamaSupirTextEdit.ReadOnly = True
            .Mitra_idSearchLookUpEdit.ReadOnly = True
            .AsalMuatanTextEdit.ReadOnly = True
            .TujuanMuatanTextEdit.ReadOnly = True
            .BeratMasukTextEdit.ReadOnly = True
            .BeratKeluarTextEdit.ReadOnly = True
            .BeratNettoTextEdit.ReadOnly = True
            .StatusTransaksiTextEdit.ReadOnly = True
            .NoBuktiTextEdit.ReadOnly = True
            .KeteranganTextEdit.ReadOnly = True

            ' Hide save button and change cancel button text
            .btnSave.Visible = False
            .btnCancel.Text = "Close"

            .ShowDialog()
        End With
    End Sub

    Private Sub saveAdd()
        Try
            ' Get the current entity from binding source
            Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)

            ' Validate required fields before proceeding
            If String.IsNullOrEmpty(o.NoTransaksi) Then
                MessageBox.Show("Nomor Transaksi tidak boleh kosong", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                NoTransaksiTextEdit.Focus()
                Return
            End If

            If o.TanggalMasuk = Nothing Then
                MessageBox.Show("Tanggal Masuk tidak boleh kosong", "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                TanggalMasukDateEdit.Focus()
                Return
            End If

            ' Create a new context for this operation
            Using dc As New dxjbt2Entities
                ' Create a new detached entity to avoid tracking issues
                Dim newEntity As New tr_jembatan_timbang()

                ' Copy all properties with date validation
                newEntity.NoTransaksi = o.NoTransaksi

                ' Set LastUpdate to current date and time
                newEntity.LastUpdate = DateTime.Now

                ' Validate TanggalMasuk is within SQL Server datetime range (1753-01-01 to 9999-12-31)
                ' TanggalMasuk is not nullable
                Dim minDate As New DateTime(1753, 1, 1)
                Dim maxDate As New DateTime(9999, 12, 31)

                If o.TanggalMasuk < minDate Then
                    newEntity.TanggalMasuk = minDate
                ElseIf o.TanggalMasuk > maxDate Then
                    newEntity.TanggalMasuk = maxDate
                Else
                    newEntity.TanggalMasuk = o.TanggalMasuk
                End If

                ' Validate TanggalKeluar is within SQL Server datetime range
                ' Using the same minDate and maxDate variables from above
                If o.TanggalKeluar.HasValue Then
                    If o.TanggalKeluar.Value < minDate Then
                        newEntity.TanggalKeluar = minDate
                    ElseIf o.TanggalKeluar.Value > maxDate Then
                        newEntity.TanggalKeluar = maxDate
                    Else
                        newEntity.TanggalKeluar = o.TanggalKeluar
                    End If
                End If

                ' Copy remaining properties
                newEntity.Kendaraan_id = o.Kendaraan_id
                newEntity.NoPolisi = o.NoPolisi
                newEntity.BeratMasuk = o.BeratMasuk
                newEntity.BeratKeluar = o.BeratKeluar
                newEntity.BeratNetto = o.BeratNetto
                newEntity.StatusTransaksi = o.StatusTransaksi
                newEntity.Mitra_id = o.Mitra_id
                newEntity.NamaSupir = o.NamaSupir
                newEntity.KodeMitra = o.KodeMitra
                newEntity.NamaMitra = o.NamaMitra

                ' Retrieve mitra code, mitra name, and driver name from tm_kendaraan
                If newEntity.Kendaraan_id > 0 Then
                    Dim kendaraan = (From k In dc.tm_kendaraan Where k.Id = newEntity.Kendaraan_id Select k).FirstOrDefault()
                    If kendaraan IsNot Nothing Then
                        ' Set driver name from kendaraan
                        If Not String.IsNullOrEmpty(kendaraan.NamaSupir) Then
                            newEntity.NamaSupir = kendaraan.NamaSupir
                        End If

                        ' Set mitra from kendaraan if available
                        If kendaraan.Mitra_id > 0 Then
                            newEntity.Mitra_id = kendaraan.Mitra_id

                            ' Get mitra details
                            Dim mitra = (From m In dc.tm_mitra Where m.Id = kendaraan.Mitra_id Select m).FirstOrDefault()
                            If mitra IsNot Nothing Then
                                ' Store mitra code and name in appropriate fields if they exist
                                newEntity.KodeMitra = mitra.KodeMitra
                                newEntity.NamaMitra = mitra.NamaMitra
                            End If
                        End If
                    End If
                End If

                ' Add and save the new entity
                dc.tr_jembatan_timbang.Add(newEntity)
                dc.SaveChanges()

                ' Update the ID in the original entity
                o.Id = newEntity.Id
            End Using
            modRefreshData(o.Id.ToString)

            ' Check if this is a completed weighing (both entry and exit weights recorded)
            If o.BeratMasuk > 0 AndAlso o.BeratKeluar.HasValue AndAlso o.BeratKeluar.Value > 0 AndAlso o.StatusTransaksi = "CLOSED" Then
                ' Load complete data with related entities for printing
                Using printDc As New dxjbt2Entities
                    Dim completeTransaction = (From t In printDc.tr_jembatan_timbang.Include("tm_kendaraan").Include("tm_mitra").Include("tr_kemasan_detail")
                                              Where t.Id = o.Id
                                              Select t).FirstOrDefault()

                    If completeTransaction IsNot Nothing Then
                        ' Create slip data DTO to avoid context disposal issues
                        Dim slipData = WeighingSlipReport.CreateSlipData(completeTransaction)
                        ShowPrintSlipDialog(slipData)
                    End If
                End Using
            End If

            Dim dr As DialogResult = MessageBox.Show("Successfully Saved!" & vbCrLf & "Do you want to add another transaction?", "Save", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
            If dr = Windows.Forms.DialogResult.Yes Then
                TrjembatantimbangBindingSource.AddNew()
                Dim newObj = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
                newObj.TanggalMasuk = DateTime.Now
                newObj.StatusTransaksi = "OPEN"
                newObj.NoTransaksi = GenerateTransactionNumber()
                NoTransaksiTextEdit.Focus()
            Else
                isClosing = True
            End If
        Catch ex As System.Data.Entity.Validation.DbEntityValidationException
            ' Handle Entity Framework validation errors
            Dim errorMessages As New System.Text.StringBuilder("Penyimpanan gagal! Terdapat kesalahan validasi:" & vbCrLf & vbCrLf)

            ' Loop through each validation error
            For Each validationError In ex.EntityValidationErrors
                For Each _err In validationError.ValidationErrors
                    ' Translate property names to user-friendly field names
                    Dim fieldName As String = _err.PropertyName
                    Select Case _err.PropertyName
                        Case "NoTransaksi"
                            fieldName = "Nomor Transaksi"
                        Case "TanggalMasuk"
                            fieldName = "Tanggal Masuk"
                        Case "Kendaraan_id"
                            fieldName = "Kendaraan"
                        Case "NoPolisi"
                            fieldName = "Nomor Polisi"
                        Case "NamaSupir"
                            fieldName = "Nama Supir"
                        Case "Mitra_id"
                            fieldName = "Mitra"
                        Case "BeratMasuk"
                            fieldName = "Berat Masuk"
                        Case "BeratKeluar"
                            fieldName = "Berat Keluar"
                        Case "StatusTransaksi"
                            fieldName = "Status Transaksi"
                    End Select

                    errorMessages.AppendLine("- Field '" & fieldName & "': " & _err.ErrorMessage)
                Next
            Next

            MessageBox.Show(errorMessages.ToString(), "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)

        Catch ex As Exception
            ' Handle specific validation exceptions with more detailed messages
            If ex.Message.Contains("not be null") Or ex.Message.Contains("NULL") Then
                ' Extract field name from error message if possible
                Dim fieldName As String = "Unknown field"
                If ex.Message.Contains("NoTransaksi") Then
                    fieldName = "Nomor Transaksi"
                ElseIf ex.Message.Contains("TanggalMasuk") Then
                    fieldName = "Tanggal Masuk"
                ElseIf ex.Message.Contains("Kendaraan_id") Then
                    fieldName = "Kendaraan"
                ElseIf ex.Message.Contains("NoPolisi") Then
                    fieldName = "Nomor Polisi"
                ElseIf ex.Message.Contains("NamaSupir") Then
                    fieldName = "Nama Supir"
                ElseIf ex.Message.Contains("Mitra_id") Then
                    fieldName = "Mitra"
                ElseIf ex.Message.Contains("BeratMasuk") Then
                    fieldName = "Berat Masuk"
                ElseIf ex.Message.Contains("BeratKeluar") Then
                    fieldName = "Berat Keluar"
                ElseIf ex.Message.Contains("StatusTransaksi") Then
                    fieldName = "Status Transaksi"
                End If

                MessageBox.Show("Penyimpanan gagal! Field " & fieldName & " tidak boleh kosong." & vbCrLf & vbCrLf &
                              "Detail error: " & ex.Message, "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            ElseIf ex.Message.Contains("duplicate") Or ex.Message.Contains("UNIQUE") Then
                ' Handle duplicate key errors
                MessageBox.Show("Penyimpanan gagal! Data dengan nomor transaksi ini sudah ada dalam database." & vbCrLf & vbCrLf &
                              "Detail error: " & ex.Message, "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            ElseIf ex.Message.Contains("foreign key") Or ex.Message.Contains("FOREIGN KEY") Then
                ' Handle foreign key errors
                MessageBox.Show("Penyimpanan gagal! Referensi ke data master (Kendaraan atau Mitra) tidak valid." & vbCrLf & vbCrLf &
                              "Detail error: " & ex.Message, "Validasi Data", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Else
                ' General error message for other exceptions
                MessageBox.Show("Penyimpanan gagal! " & vbCrLf & vbCrLf & "Detail error: " & ex.Message,
                               "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        End Try
    End Sub



    Private Sub BeratKeluarTextEdit_EditValueChanged(sender As Object, e As EventArgs) Handles BeratKeluarTextEdit.EditValueChanged
        ' Auto calculate BeratNetto when BeratKeluar changes
        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
        If o.BeratMasuk > 0 AndAlso o.BeratKeluar > 0 Then
            o.BeratNetto = Math.Abs(CDec(o.BeratKeluar) - CDec(o.BeratMasuk))
            BeratNettoTextEdit.EditValue = o.BeratNetto
        End If
    End Sub

    Private Sub BeratMasukTextEdit_EditValueChanged(sender As Object, e As EventArgs) Handles BeratMasukTextEdit.EditValueChanged
        ' Auto calculate BeratNetto when BeratMasuk changes
        Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
        If o.BeratMasuk > 0 AndAlso o.BeratKeluar > 0 Then
            o.BeratNetto = Math.Abs(CDec(o.BeratKeluar) - CDec(o.BeratMasuk))
            BeratNettoTextEdit.EditValue = o.BeratNetto
        End If
    End Sub

    Private Sub TanggalKeluarDateEdit_EditValueChanged(sender As Object, e As EventArgs) Handles TanggalKeluarDateEdit.EditValueChanged
        ' If TanggalKeluar is set, update status to CLOSED
        If TrjembatantimbangBindingSource IsNot Nothing AndAlso TrjembatantimbangBindingSource.Current IsNot Nothing Then
            Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
            If o IsNot Nothing AndAlso o.TanggalKeluar.HasValue AndAlso o.StatusTransaksi = "OPEN" Then
                o.StatusTransaksi = "CLOSED"
                If StatusTransaksiTextEdit IsNot Nothing Then
                    StatusTransaksiTextEdit.EditValue = o.StatusTransaksi
                End If
            End If
        End If
    End Sub

    Private Sub Kendaraan_idSearchLookUpEdit_EditValueChanged(sender As Object, e As EventArgs) Handles Kendaraan_idSearchLookUpEdit.EditValueChanged
        Try
            ' Get selected kendaraan ID
            Dim kendaraanId As Integer = Convert.ToInt32(Kendaraan_idSearchLookUpEdit.EditValue)

            ' Skip if no kendaraan selected
            If kendaraanId <= 0 Then
                Return
            End If

            ' Check if this kendaraan has an open transaction (entry weight recorded but not exit weight)
            Using dc As New dxjbt2Entities
                Dim openTransaction = (From t In dc.tr_jembatan_timbang
                                       Where t.Kendaraan_id = kendaraanId AndAlso
                                            t.StatusTransaksi = "OPEN" AndAlso
                                            t.BeratMasuk > 0 AndAlso
                                            (t.BeratKeluar = 0 OrElse t.BeratKeluar Is Nothing) AndAlso
                                            t.TanggalKeluar Is Nothing
                                       Select t).FirstOrDefault()

                If openTransaction IsNot Nothing Then
                    ' Found an open transaction for this kendaraan
                    MessageBox.Show("Kendaraan ini sudah melakukan penimbangan masuk pada " &
                                   openTransaction.TanggalMasuk.ToString("dd MMM yyyy HH:mm") &
                                   ". Form akan beralih ke mode penimbangan keluar.",
                                   "Penimbangan Keluar", MessageBoxButtons.OK, MessageBoxIcon.Information)

                    ' Load the open transaction
                    TrjembatantimbangBindingSource.DataSource = (From c In dc.tr_jembatan_timbang Where c.Id = openTransaction.Id Select c).ToList()

                    ' Set UI to exit weighing mode and update the data model
                    Dim currentDateTime As DateTime = DateTime.Now
                    TanggalKeluarDateEdit.EditValue = currentDateTime

                    ' Make sure the data model is also updated
                    Dim currentTransaction = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
                    If currentTransaction IsNot Nothing Then
                        currentTransaction.TanggalKeluar = currentDateTime
                    End If

                    ' Make kendaraan field read-only since we're in exit mode
                    Kendaraan_idSearchLookUpEdit.ReadOnly = True

                    ' Switch to edit mode instead of add mode
                    dbAction = "edit"
                End If

                ' When kendaraan is selected, populate NoPolisi field, driver name, and mitra
                Dim kendaraan = (From k In dc.tm_kendaraan Where k.Id = kendaraanId Select k).FirstOrDefault()
                If kendaraan IsNot Nothing AndAlso TrjembatantimbangBindingSource.Current IsNot Nothing Then
                    Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)
                    o.NoPolisi = kendaraan.NoPolisi

                    If Not String.IsNullOrEmpty(kendaraan.NamaSupir) Then
                        o.NamaSupir = kendaraan.NamaSupir
                        NamaSupirTextEdit.EditValue = kendaraan.NamaSupir
                    End If

                    If kendaraan.Mitra_id > 0 Then
                        o.Mitra_id = kendaraan.Mitra_id
                        Mitra_idSearchLookUpEdit.EditValue = kendaraan.Mitra_id
                    End If
                End If
            End Using
        Catch ex As Exception
            MessageBox.Show("Error saat memeriksa transaksi kendaraan: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Initialize and open serial port connection
    Private Sub InitializeSerialPort()
        Try
            ' Check if dummy mode is enabled
            dummyModeEnabled = SettingsHelper.IsDummyModeEnabled()

            If dummyModeEnabled Then
                ' Initialize dummy mode timer
                InitializeDummyMode()
            Else
                ' Close existing connection if open
                If serialPort IsNot Nothing AndAlso serialPort.IsOpen Then
                    serialPort.Close()
                End If

                ' Create new serial port
                serialPort = New SerialPort()
                With serialPort
                    ' Get COM port from settings
                    .PortName = SettingsHelper.GetWeighbridgeComPort() ' Get from database settings
                    .BaudRate = SettingsHelper.GetWeighbridgeBaudRate() ' Get from database settings
                    .DataBits = 8
                    .Parity = Parity.None
                    .StopBits = StopBits.One
                    .Handshake = Handshake.None
                    .ReadTimeout = 500
                    .WriteTimeout = 500
                    .Open()
                End With
            End If

            ' Add button to form to read weight
            AddWeightReadButton()

        Catch ex As Exception
            MessageBox.Show("Error connecting to weighbridge: " & ex.Message & vbCrLf &
                          "Enable dummy mode in Settings if no hardware is connected.",
                          "COM Port Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Initialize dummy mode for development without hardware
    Private Sub InitializeDummyMode()
        ' Create timer to simulate weight readings
        dummyTimer = New Timer()
        dummyTimer.Interval = 2000 ' 2 seconds
        AddHandler dummyTimer.Tick, AddressOf DummyTimer_Tick

        ' Start the timer immediately to show real-time weight
        dummyTimer.Start()

        ' Show dummy mode indicator
        Dim lblDummyMode As New DevExpress.XtraEditors.LabelControl()
        With lblDummyMode
            .Name = "lblDummyMode"
            .Text = "DUMMY MODE"
            .Location = New Point(12, 12)
            .Size = New Size(120, 20)
            .Appearance.Font = New Font(lblDummyMode.Appearance.Font.FontFamily, 10, FontStyle.Bold)
            .Appearance.ForeColor = Color.Red
            .Appearance.BackColor = Color.Yellow
            .Appearance.Options.UseBackColor = True
            .AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
            .BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple
            .Visible = True
        End With
        Me.Controls.Add(lblDummyMode)
    End Sub

    ' Simulate weight readings in dummy mode
    Private Sub DummyTimer_Tick(sender As Object, e As EventArgs)
        ' Generate random weight between 1000-20000 kg
        Dim random As New Random()
        Dim weight As Integer = random.Next(1000, 20000)

        ' Simulate weight data format from weighbridge
        weightBuffer = weight.ToString() & vbCr

        ' Process the dummy weight data
        ProcessWeightData()
    End Sub

    ' Handle data received from serial port
    Private Sub serialPort_DataReceived(sender As Object, e As SerialDataReceivedEventArgs) Handles serialPort.DataReceived
        Try
            ' Read data from serial port
            Dim data As String = serialPort.ReadExisting()

            ' Append to buffer
            weightBuffer &= data

            ' Check if we have a complete weight reading (usually ends with CR or LF)
            If weightBuffer.Contains(vbCr) OrElse weightBuffer.Contains(vbLf) Then
                ' Process on UI thread to update real-time display
                Me.Invoke(New Action(AddressOf ProcessWeightData))
            End If
        Catch ex As Exception
            ' Handle errors
        End Try
    End Sub

    ' Current weight value from weighbridge
    Private currentWeight As Decimal = 0

    ' Process weight data from buffer - only updates the real-time display
    Private Sub ProcessWeightData()
        Try
            ' Extract weight value from buffer
            ' This will need to be adjusted based on your weighbridge output format
            Dim lines() As String = weightBuffer.Split(New String() {vbCr, vbLf}, StringSplitOptions.RemoveEmptyEntries)

            If lines.Length > 0 Then
                ' Get the last complete line
                Dim weightLine As String = lines(lines.Length - 1)

                ' Extract numeric value - adjust regex based on your weighbridge output format
                Dim weightMatch As System.Text.RegularExpressions.Match = System.Text.RegularExpressions.Regex.Match(weightLine, "\d+")

                If weightMatch.Success Then
                    Dim weight As Decimal

                    If Decimal.TryParse(weightMatch.Value, weight) Then
                        ' Store current weight value
                        currentWeight = weight

                        ' Update real-time weight display label only
                        Dim lblWeight = Me.Controls.Find("lbl_berat_jembatan_timbang", True).FirstOrDefault()
                        If lblWeight IsNot Nothing Then
                            DirectCast(lblWeight, DevExpress.XtraEditors.LabelControl).Text = weight.ToString("#,##0") & " kg"
                        End If
                    End If
                End If
            End If

            ' Clear buffer
            weightBuffer = ""

        Catch ex As Exception
            MessageBox.Show("Error processing weight data: " & ex.Message, "Weight Data Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Add button to form to read weight and real-time weight display label
    Private Sub AddWeightReadButton()
        ' Create button
        Dim btnReadWeight As New DevExpress.XtraEditors.SimpleButton()
        With btnReadWeight
            .Name = "btnReadWeight"
            .Text = "Read Weight"
            .Location = New Point(btnSave.Location.X - 110, btnSave.Location.Y)
            .Size = New Size(100, btnSave.Height)
            .Visible = True
            AddHandler .Click, AddressOf btnReadWeight_Click
        End With

        ' Create real-time weight display label
        Dim lblWeightTitle As New DevExpress.XtraEditors.LabelControl()
        With lblWeightTitle
            .Name = "lblWeightTitle"
            .Text = "CURRENT WEIGHT:"
            .Location = New Point((Me.Width - 220) \ 2, btnReadWeight.Location.Y - 60)
            .Size = New Size(220, 40) ' Lebih lebar dan tinggi
            .Appearance.Font = New Font(lblWeightTitle.Appearance.Font.FontFamily, 18, FontStyle.Bold)
            .Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            .Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
            .Appearance.ForeColor = Color.Navy
            .Visible = True
        End With

        Dim lblBeratJembatanTimbang As New DevExpress.XtraEditors.LabelControl()
        With lblBeratJembatanTimbang
            .Name = "lbl_berat_jembatan_timbang"
            .Text = "0 KG"
            .Location = New Point(lblWeightTitle.Location.X + 10, lblWeightTitle.Location.Y + 45)
            .Size = New Size(200, 40)
            .Appearance.Font = New Font(lblBeratJembatanTimbang.Appearance.Font.FontFamily, 22, FontStyle.Bold)
            .Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
            .Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
            .Appearance.ForeColor = Color.Red
            .Visible = True
        End With

        ' Add to form
        Me.Controls.Add(btnReadWeight)
        Me.Controls.Add(lblWeightTitle)
        Me.Controls.Add(lblBeratJembatanTimbang)
    End Sub

    ' Handle read weight button click - records current weight to BeratMasuk or BeratKeluar
    Private Sub btnReadWeight_Click(sender As Object, e As EventArgs)
        Try
            ' Make sure we have a current weight value
            If currentWeight <= 0 Then
                ' If no current weight, try to get one
                If SettingsHelper.IsDummyModeEnabled() Then
                    ' Generate random weight between 1000-20000 kg
                    Dim random As New Random()
                    currentWeight = random.Next(1000, 20000)

                    ' Update display
                    Dim lblWeight = Me.Controls.Find("lbl_berat_jembatan_timbang", True).FirstOrDefault()
                    If lblWeight IsNot Nothing Then
                        DirectCast(lblWeight, DevExpress.XtraEditors.LabelControl).Text = currentWeight.ToString("#,##0") & " kg"
                    End If

                    ' Start timer if not already running
                    If dummyTimer IsNot Nothing AndAlso Not dummyTimer.Enabled Then
                        dummyTimer.Start()
                    End If
                Else
                    ' Request weight from weighbridge
                    ' This may vary depending on your weighbridge protocol
                    ' Some may require a specific command to be sent
                    ' For now, we'll just process any data in the buffer

                    If weightBuffer.Length > 0 Then
                        ProcessWeightData()
                    Else
                        MessageBox.Show("No weight data available. Please ensure the weighbridge is connected and sending data.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information)
                        Return ' Exit if no weight data
                    End If
                End If
            End If

            ' Record the current weight to the appropriate field
            If TrjembatantimbangBindingSource IsNot Nothing AndAlso TrjembatantimbangBindingSource.Current IsNot Nothing Then
                Dim o = CType(TrjembatantimbangBindingSource.Current, tr_jembatan_timbang)

                ' Update BeratMasuk if it's zero, otherwise update BeratKeluar
                If o.BeratMasuk = 0 Then
                    o.BeratMasuk = currentWeight
                    BeratMasukTextEdit.EditValue = currentWeight
                    MessageBox.Show("Weight recorded as entry weight (Berat Masuk).", "Weight Recorded", MessageBoxButtons.OK, MessageBoxIcon.Information)
                ElseIf o.BeratKeluar OrElse o.BeratKeluar Is Nothing Then
                    o.BeratKeluar = currentWeight
                    BeratKeluarTextEdit.EditValue = currentWeight

                    ' Calculate net weight
                    o.BeratNetto = Math.Abs(CDec(o.BeratKeluar) - CDec(o.BeratMasuk))
                    BeratNettoTextEdit.EditValue = o.BeratNetto

                    ' Update exit date and status
                    If o.TanggalKeluar Is Nothing Then
                        o.TanggalKeluar = DateTime.Now
                        TanggalKeluarDateEdit.EditValue = o.TanggalKeluar

                        If o.StatusTransaksi = "OPEN" Then
                            o.StatusTransaksi = "CLOSED"
                            StatusTransaksiTextEdit.EditValue = o.StatusTransaksi
                        End If
                    End If

                    MessageBox.Show("Weight recorded as exit weight (Berat Keluar) and net weight calculated.", "Weight Recorded", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    MessageBox.Show("Both entry and exit weights are already recorded. Reset the form to record new weights.", "Weights Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            End If

        Catch ex As Exception
            MessageBox.Show("Error recording weight: " & ex.Message, "Weight Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Close serial port when form closes
    Private Sub frmJembatanTimbang_edit_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        ' Close serial port if open
        If serialPort IsNot Nothing AndAlso serialPort.IsOpen Then
            serialPort.Close()
        End If

        ' Stop dummy timer if running
        If dummyTimer IsNot Nothing AndAlso dummyTimer.Enabled Then
            dummyTimer.Stop()
        End If
    End Sub
End Class
