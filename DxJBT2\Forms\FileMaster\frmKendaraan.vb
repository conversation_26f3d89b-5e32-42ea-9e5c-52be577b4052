Imports DxJBT2

Public Class frmKendaraan
    Inherits frmBaseList

    Public Sub New()
        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        refreshData("")
    End Sub

    Public Function CreateInstance() As frmKendaraan
        Dim f As frmKendaraan
        ThisForm = Nothing
        If ThisForm Is Nothing Then
            ThisForm = New frmKendaraan
        End If
        f = DirectCast(ThisForm, frmKendaraan)
        Return f
    End Function

    Public Sub DbAction(msg As String)
        Select Case msg.Trim.ToUpper
            Case "ADD"
                addRecord()
            Case "EDIT"
                editRecord()
            Case "VIEW"
                viewRecord()
            Case "DELETE"
                DeleteRecord()
            Case Else
                Close()
        End Select
    End Sub

    Private Sub editRecord()
        Dim o = CType(TmkendaraanBindingSource.Current, tm_kendaraan)
        If o Is Nothing Then
            Return
        End If

        frmKendaraan_edit.showAs_Edit(New frmKendaraan_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub refreshData(ByVal s As String)
        Dim dc As New dxjbt2Entities
        Dim q = From c In dc.tm_kendaraan Order By c.NoPolisi Select c
        TmkendaraanBindingSource.DataSource = q.ToList
    End Sub

    Private Sub addRecord()
        frmKendaraan_edit.showAs_Add(New frmKendaraan_edit.MsgHandler(AddressOf refreshData))
    End Sub

    Private Sub viewRecord()
        Dim o = CType(TmkendaraanBindingSource.Current, tm_kendaraan)
        If o Is Nothing Then
            Return
        End If

        frmKendaraan_edit.showAs_View(New frmKendaraan_edit.MsgHandler(AddressOf refreshData), o.Id)
    End Sub

    Private Sub DeleteRecord()
        Dim o = CType(TmkendaraanBindingSource.Current, tm_kendaraan)
        If o Is Nothing Then
            Return
        End If

        Dim dr As DialogResult = MessageBox.Show("Are you sure you want to delete this vehicle?" & vbCrLf &
                                               "No Polisi: " & o.NoPolisi & vbCrLf &
                                               "Driver: " & o.NamaSupir,
                                               "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

        If dr = DialogResult.Yes Then
            Try
                Dim dc As New dxjbt2Entities
                Dim q = From c In dc.tm_kendaraan Where c.Id = o.Id
                dc.tm_kendaraan.Remove(q.FirstOrDefault)
                dc.SaveChanges()
                refreshData("")
            Catch ex As Exception
                MessageBox.Show("Delete Failed: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class