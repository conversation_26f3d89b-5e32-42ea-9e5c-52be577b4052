﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B4B42701-BF80-466E-834F-6A2BBC4295B3}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>DxJBT2.Startup</StartupObject>
    <RootNamespace>DxJBT2</RootNamespace>
    <AssemblyName>DxJBT2</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>WindowsApp1.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>WindowsApp1.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v24.1" />
    <Reference Include="DevExpress.Data.v24.1" />
    <Reference Include="DevExpress.Data.Desktop.v24.1" />
    <Reference Include="DevExpress.Printing.v24.1.Core" />
    <Reference Include="DevExpress.Utils.v24.1" />
    <Reference Include="DevExpress.XtraBars.v24.1" />
    <Reference Include="DevExpress.Sparkline.v24.1.Core" />
    <Reference Include="DevExpress.XtraEditors.v24.1" />
    <Reference Include="DevExpress.Drawing.v24.1" />
    <Reference Include="DevExpress.XtraGrid.v24.1, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v24.1, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPrinting.v24.1, Version=24.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Data\dxjbt2Model.Context.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>dxjbt2Model.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\dxjbt2Model.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>dxjbt2Model.edmx</DependentUpon>
    </Compile>
    <Compile Include="Data\dxjbt2Model.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\sp_GetUserMenuAccess_Result.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_kemasan.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_kendaraan.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_menu.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_mitra.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_role.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_role_menu_access.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_settings.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_tipe_kendaraan.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tm_user.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tr_jembatan_timbang.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\tr_kemasan_detail.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\vw_jembatan_timbang_with_kemasan.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\vw_role_menu_access.vb">
      <DependentUpon>dxjbt2Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKendaraan.Designer.vb">
      <DependentUpon>frmKendaraan.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKemasan.Designer.vb">
      <DependentUpon>frmKemasan.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKemasan.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKemasan_edit.Designer.vb">
      <DependentUpon>frmKemasan_edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKemasan_edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKendaraan.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKendaraan_edit.Designer.vb">
      <DependentUpon>frmKendaraan_edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmKendaraan_edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileMaster\frmMitra.Designer.vb">
      <DependentUpon>frmMitra.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmMitra.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileMaster\frmMitra_edit.Designer.vb">
      <DependentUpon>frmMitra_edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmMitra_edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileMaster\frmTipeKendaraan.Designer.vb">
      <DependentUpon>frmTipeKendaraan.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmTipeKendaraan.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FileMaster\frmTipeKendaraan_Edit.Designer.vb">
      <DependentUpon>frmTipeKendaraan_Edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FileMaster\frmTipeKendaraan_Edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Transaksi\frmKemasanEntry.Designer.vb">
      <DependentUpon>frmKemasanEntry.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Transaksi\frmKemasanEntry.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmBaseList.Designer.vb">
      <DependentUpon>frmBaseList.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmBaseList.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmEntryBase.Designer.vb">
      <DependentUpon>frmEntryBase.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmEntryBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Master\frmSettings.Designer.vb">
      <DependentUpon>frmSettings.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Master\frmSettings.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Security\frmLogin.Designer.vb">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Security\frmLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Security\frmRole.Designer.vb">
      <DependentUpon>frmRole.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Security\frmRole.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Security\frmUser.Designer.vb">
      <DependentUpon>frmUser.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Security\frmUser.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Transaksi\frmJembatanTimbang.Designer.vb">
      <DependentUpon>frmJembatanTimbang.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Transaksi\frmJembatanTimbang.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Transaksi\frmJembatanTimbang_edit.Designer.vb">
      <DependentUpon>frmJembatanTimbang_edit.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Transaksi\frmJembatanTimbang_edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Transaksi\frmKemasanDetail.Designer.vb">
      <DependentUpon>frmKemasanDetail.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Transaksi\frmKemasanDetail.vb" />
    <Compile Include="Helpers\PasswordHelper.vb" />
    <Compile Include="Helpers\SecurityHelper.vb" />
    <Compile Include="Helpers\SettingsHelper.vb" />
    <Compile Include="Reports\WeighingSlipReport.vb" />
    <Compile Include="MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\Application.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Startup.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\FileMaster\frmKemasan_edit.resx">
      <DependentUpon>frmKemasan_edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FileMaster\frmKendaraan.resx">
      <DependentUpon>frmKendaraan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FileMaster\frmKendaraan_edit.resx">
      <DependentUpon>frmKendaraan_edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FileMaster\frmMitra.resx">
      <DependentUpon>frmMitra.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FileMaster\frmMitra_edit.resx">
      <DependentUpon>frmMitra_edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FileMaster\frmTipeKendaraan.resx">
      <DependentUpon>frmTipeKendaraan.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FileMaster\frmTipeKendaraan_Edit.resx">
      <DependentUpon>frmTipeKendaraan_Edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmBaseList.resx">
      <DependentUpon>frmBaseList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmEntryBase.resx">
      <DependentUpon>frmEntryBase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Security\frmLogin.resx">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Transaksi\frmJembatanTimbang_edit.resx">
      <DependentUpon>frmJembatanTimbang_edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="Data\dxjbt2Model.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>dxjbt2Model.Designer.vb</LastGenOutput>
    </EntityDeploy>
    <None Include="Data\dxjbt2Model.edmx.diagram">
      <DependentUpon>dxjbt2Model.edmx</DependentUpon>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\DataSources\tm_kendaraan.datasource" />
    <None Include="My Project\DataSources\tm_mitra.datasource" />
    <None Include="My Project\DataSources\tm_tipe_kendaraan.datasource" />
    <None Include="My Project\DataSources\tr_jembatan_timbang.datasource" />
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\BOOrderItem_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Data\dxjbt2Model.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>dxjbt2Model.Context.vb</LastGenOutput>
      <DependentUpon>dxjbt2Model.edmx</DependentUpon>
    </Content>
    <Content Include="Data\dxjbt2Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>dxjbt2Model.edmx</DependentUpon>
      <LastGenOutput>dxjbt2Model.vb</LastGenOutput>
    </Content>
    <Content Include="dxjbt2.mdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="dxjbt2_log.ldf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>dxjbt2.mdf</DependentUpon>
    </Content>
    <None Include="Resources\User_32x32.png" />
    <None Include="Resources\Administrasi_32x32.png" />
    <Content Include="Resources\JembatanTimbang_32x32.png" />
    <None Include="Resources\Kendaraan_32x32.png" />
    <None Include="Resources\TipeKendaraan_32x32.png" />
    <None Include="Resources\Security_16x16.png" />
    <None Include="Resources\Role_32x32.png" />
    <Content Include="Resources\Setting_32x32.png" />
    <Content Include="Resources\Transaksi_32x32.png" />
    <None Include="Resources\BOContact_16x161.png" />
    <None Include="Resources\BOContact_16x16.png" />
    <None Include="Resources\Show_32x32.png" />
    <None Include="Resources\Close_32x32.png" />
    <None Include="Resources\DeleteList_32x32.png" />
    <None Include="Resources\EditName_32x32.png" />
    <None Include="Resources\BOReport2_32x32.png" />
    <None Include="Resources\BOTask_32x32.png" />
    <None Include="Resources\AddFile_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
</Project>