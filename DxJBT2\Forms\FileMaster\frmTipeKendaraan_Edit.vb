﻿Imports System.ComponentModel

Public Class frmTipeKendaraan_Edit
    Inherits frmEntryBase

    Private modRefreshData As MsgHandler
    Private dbAction As String
    Public Sub showAs_Add(ByVal msg As MsgHandler)
        Dim f As frmTipeKendaraan_Edit = New frmTipeKendaraan_Edit
        With f
            .modRefreshData = msg
            .dbAction = "add"

            Dim dc As New dxjbt2Entities

            .TmtipekendaraanBindingSource.DataSource = (From c In dc.tm_tipe_kendaraan Where c.Id = 0 Select c).ToList
            .TmtipekendaraanBindingSource.AddNew()

            .ShowDialog()
        End With
    End Sub
    Public Sub showAs_Edit(ByVal msg As MsgHandler, ByVal _id As Integer)
        Dim f As frmTipeKendaraan_Edit = New frmTipeKendaraan_Edit
        With f
            .modRefreshData = msg
            .dbAction = "edit"

            Dim dc As New dxjbt2Entities
            .TmtipekendaraanBindingSource.DataSource = (From c In dc.tm_tipe_kendaraan Where c.Id = _id Select c).ToList

            .btnSave.Text = "Update"
            .btnCancel.Text = "Close"
            .ShowDialog()
        End With
    End Sub
    Public Sub showAs_View(ByVal msg As MsgHandler, ByVal _id As Integer)
        Dim f As frmTipeKendaraan_Edit = New frmTipeKendaraan_Edit
        With f
            .modRefreshData = msg
            .dbAction = "edit"

            Dim dc As New dxjbt2Entities
            .TmtipekendaraanBindingSource.DataSource = (From c In dc.tm_tipe_kendaraan Where c.Id = _id Select c).ToList

            .btnSave.Visible = False
            .btnCancel.Text = "Close"
            .ShowDialog()
        End With
    End Sub

    Private Sub btnSave_Click(sender As Object, e As EventArgs) Handles btnSave.Click
        If DxValidationProvider1.Validate = False Then
            Return
        End If
        If dbAction = "add" Then
            saveAdd()
        Else
            saveEdit()
        End If
        Close()
    End Sub

    Private Sub NamaTipeTextEdit_KeyPress(sender As Object, e As KeyPressEventArgs) Handles NamaTipeTextEdit.KeyPress
        e.KeyChar = e.KeyChar.ToString.ToUpper
    End Sub
    Private Sub saveAdd()
        Dim dc As New dxjbt2Entities
        Try
            Dim tTipe As tm_tipe_kendaraan = CType(TmtipekendaraanBindingSource.Current, tm_tipe_kendaraan)
            dc.tm_tipe_kendaraan.Add(tTipe)
            dc.SaveChanges()
            modRefreshData(tTipe.Id.ToString)
            Dim dr As DialogResult = MessageBox.Show("Successfully Saved!" & vbCrLf & "Do you want to add another data?", "Save", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
            If dr = Windows.Forms.DialogResult.Yes Then
                TmtipekendaraanBindingSource.AddNew()
                NamaTipeTextEdit.Focus()
            Else
                isClosing = True
            End If
        Catch ex As Exception
            MessageBox.Show("Save Failed! : " & ex.Message, "Save", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub saveEdit()
        Dim dc As New dxjbt2Entities
        Try
            Dim tTipe As tm_tipe_kendaraan = CType(TmtipekendaraanBindingSource.Current, tm_tipe_kendaraan)
            Dim q = From c In dc.tm_tipe_kendaraan Where c.Id = tTipe.Id
            With q.FirstOrDefault
                .NamaTipe = tTipe.NamaTipe
            End With
            dc.SaveChanges()
            modRefreshData(tTipe.Id.ToString)
            MessageBox.Show("Successfully Updated!", "Update", MessageBoxButtons.OK, MessageBoxIcon.Question)
            isClosing = True
        Catch ex As Exception
            MessageBox.Show("Update Failed! : " & ex.Message, "Update", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub TmtipekendaraanBindingSource_AddingNew(sender As Object, e As AddingNewEventArgs) Handles TmtipekendaraanBindingSource.AddingNew
        Dim tTipe As New tm_tipe_kendaraan
        'tTipe.Oid = System.Guid.NewGuid

        e.NewObject = tTipe
    End Sub

    Private Sub frmTipeKendaraan_Edit_Load(sender As Object, e As EventArgs) Handles Me.Load
        NamaTipeTextEdit.Focus()
    End Sub

    Private Sub frmTipeKendaraan_Edit_Activated(sender As Object, e As EventArgs) Handles Me.Activated
        NamaTipeTextEdit.Focus()
    End Sub
End Class