﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmKendaraan_edit
    Inherits frmEntryBase

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim ConditionValidationRule1 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule2 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Dim ConditionValidationRule3 As DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule = New DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule()
        Me.DataLayoutControl1 = New DevExpress.XtraDataLayout.DataLayoutControl()
        Me.NoPolisiTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.TmkendaraanBindingSource = New System.Windows.Forms.BindingSource(Me.components)
        Me.TipeKendaraan_idSearchLookUpEdit = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.SearchLookUpEdit1View = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.Mitra_idSearchLookUpEdit = New DevExpress.XtraEditors.SearchLookUpEdit()
        Me.GridView1 = New DevExpress.XtraGrid.Views.Grid.GridView()
        Me.NamaSupirTextEdit = New DevExpress.XtraEditors.TextEdit()
        Me.Root = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.LayoutControlGroup1 = New DevExpress.XtraLayout.LayoutControlGroup()
        Me.ItemForNoPolisi = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForTipeKendaraan_id = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForMitra_id = New DevExpress.XtraLayout.LayoutControlItem()
        Me.ItemForNamaSupir = New DevExpress.XtraLayout.LayoutControlItem()
        Me.DxValidationProvider1 = New DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(Me.components)
        Me.GridColumn1 = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.KodeMitraCol = New DevExpress.XtraGrid.Columns.GridColumn()
        Me.NamaMitraCol = New DevExpress.XtraGrid.Columns.GridColumn()
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.DataLayoutControl1.SuspendLayout()
        CType(Me.NoPolisiTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TmkendaraanBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TipeKendaraan_idSearchLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Mitra_idSearchLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.NamaSupirTextEdit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNoPolisi, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForTipeKendaraan_id, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForMitra_id, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ItemForNamaSupir, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblHeader
        '
        Me.lblHeader.Size = New System.Drawing.Size(868, 54)
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(759, 443)
        '
        'btnSave
        '
        Me.btnSave.Location = New System.Drawing.Point(676, 443)
        '
        'DataLayoutControl1
        '
        Me.DataLayoutControl1.Controls.Add(Me.NoPolisiTextEdit)
        Me.DataLayoutControl1.Controls.Add(Me.TipeKendaraan_idSearchLookUpEdit)
        Me.DataLayoutControl1.Controls.Add(Me.Mitra_idSearchLookUpEdit)
        Me.DataLayoutControl1.Controls.Add(Me.NamaSupirTextEdit)
        Me.DataLayoutControl1.DataSource = Me.TmkendaraanBindingSource
        Me.DataLayoutControl1.Location = New System.Drawing.Point(12, 76)
        Me.DataLayoutControl1.Name = "DataLayoutControl1"
        Me.DataLayoutControl1.Root = Me.Root
        Me.DataLayoutControl1.Size = New System.Drawing.Size(523, 228)
        Me.DataLayoutControl1.TabIndex = 44
        Me.DataLayoutControl1.Text = "DataLayoutControl1"
        '
        'NoPolisiTextEdit
        '
        Me.NoPolisiTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkendaraanBindingSource, "NoPolisi", True))
        Me.NoPolisiTextEdit.Location = New System.Drawing.Point(114, 12)
        Me.NoPolisiTextEdit.Name = "NoPolisiTextEdit"
        Me.NoPolisiTextEdit.Size = New System.Drawing.Size(397, 22)
        Me.NoPolisiTextEdit.StyleController = Me.DataLayoutControl1
        Me.NoPolisiTextEdit.TabIndex = 4
        ConditionValidationRule1.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule1.ErrorText = "Wajib isi"
        Me.DxValidationProvider1.SetValidationRule(Me.NoPolisiTextEdit, ConditionValidationRule1)
        '
        'TmkendaraanBindingSource
        '
        Me.TmkendaraanBindingSource.DataSource = GetType(DxJBT2.tm_kendaraan)
        '
        'TipeKendaraan_idSearchLookUpEdit
        '
        Me.TipeKendaraan_idSearchLookUpEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkendaraanBindingSource, "TipeKendaraan_id", True))
        Me.TipeKendaraan_idSearchLookUpEdit.Location = New System.Drawing.Point(114, 38)
        Me.TipeKendaraan_idSearchLookUpEdit.Name = "TipeKendaraan_idSearchLookUpEdit"
        Me.TipeKendaraan_idSearchLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.TipeKendaraan_idSearchLookUpEdit.Properties.DisplayMember = "NamaTipe"
        Me.TipeKendaraan_idSearchLookUpEdit.Properties.NullText = ""
        Me.TipeKendaraan_idSearchLookUpEdit.Properties.PopupView = Me.SearchLookUpEdit1View
        Me.TipeKendaraan_idSearchLookUpEdit.Properties.ValueMember = "Id"
        Me.TipeKendaraan_idSearchLookUpEdit.Size = New System.Drawing.Size(397, 22)
        Me.TipeKendaraan_idSearchLookUpEdit.StyleController = Me.DataLayoutControl1
        Me.TipeKendaraan_idSearchLookUpEdit.TabIndex = 5
        ConditionValidationRule2.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule2.ErrorText = "Wajib isi"
        Me.DxValidationProvider1.SetValidationRule(Me.TipeKendaraan_idSearchLookUpEdit, ConditionValidationRule2)
        '
        'SearchLookUpEdit1View
        '
        Me.SearchLookUpEdit1View.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.GridColumn1})
        Me.SearchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.SearchLookUpEdit1View.Name = "SearchLookUpEdit1View"
        Me.SearchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.SearchLookUpEdit1View.OptionsView.ShowGroupPanel = False
        '
        'Mitra_idSearchLookUpEdit
        '
        Me.Mitra_idSearchLookUpEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkendaraanBindingSource, "Mitra_id", True))
        Me.Mitra_idSearchLookUpEdit.Location = New System.Drawing.Point(114, 64)
        Me.Mitra_idSearchLookUpEdit.Name = "Mitra_idSearchLookUpEdit"
        Me.Mitra_idSearchLookUpEdit.Properties.Buttons.AddRange(New DevExpress.XtraEditors.Controls.EditorButton() {New DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)})
        Me.Mitra_idSearchLookUpEdit.Properties.NullText = ""
        Me.Mitra_idSearchLookUpEdit.Properties.PopupView = Me.GridView1
        Me.Mitra_idSearchLookUpEdit.Properties.ValueMember = "Id"
        Me.Mitra_idSearchLookUpEdit.Size = New System.Drawing.Size(397, 22)
        Me.Mitra_idSearchLookUpEdit.StyleController = Me.DataLayoutControl1
        Me.Mitra_idSearchLookUpEdit.TabIndex = 6
        '
        'GridView1
        '
        Me.GridView1.Columns.AddRange(New DevExpress.XtraGrid.Columns.GridColumn() {Me.KodeMitraCol, Me.NamaMitraCol})
        Me.GridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus
        Me.GridView1.Name = "GridView1"
        Me.GridView1.OptionsSelection.EnableAppearanceFocusedCell = False
        Me.GridView1.OptionsView.ShowGroupPanel = False
        '
        'NamaSupirTextEdit
        '
        Me.NamaSupirTextEdit.DataBindings.Add(New System.Windows.Forms.Binding("EditValue", Me.TmkendaraanBindingSource, "NamaSupir", True))
        Me.NamaSupirTextEdit.Location = New System.Drawing.Point(114, 90)
        Me.NamaSupirTextEdit.Name = "NamaSupirTextEdit"
        Me.NamaSupirTextEdit.Size = New System.Drawing.Size(397, 22)
        Me.NamaSupirTextEdit.StyleController = Me.DataLayoutControl1
        Me.NamaSupirTextEdit.TabIndex = 7
        ConditionValidationRule3.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank
        ConditionValidationRule3.ErrorText = "Wajib isi"
        Me.DxValidationProvider1.SetValidationRule(Me.NamaSupirTextEdit, ConditionValidationRule3)
        '
        'Root
        '
        Me.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.[True]
        Me.Root.GroupBordersVisible = False
        Me.Root.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.LayoutControlGroup1})
        Me.Root.Name = "Root"
        Me.Root.Size = New System.Drawing.Size(523, 228)
        Me.Root.TextVisible = False
        '
        'LayoutControlGroup1
        '
        Me.LayoutControlGroup1.AllowDrawBackground = False
        Me.LayoutControlGroup1.GroupBordersVisible = False
        Me.LayoutControlGroup1.Items.AddRange(New DevExpress.XtraLayout.BaseLayoutItem() {Me.ItemForNoPolisi, Me.ItemForTipeKendaraan_id, Me.ItemForMitra_id, Me.ItemForNamaSupir})
        Me.LayoutControlGroup1.Location = New System.Drawing.Point(0, 0)
        Me.LayoutControlGroup1.Name = "autoGeneratedGroup0"
        Me.LayoutControlGroup1.Size = New System.Drawing.Size(503, 208)
        '
        'ItemForNoPolisi
        '
        Me.ItemForNoPolisi.Control = Me.NoPolisiTextEdit
        Me.ItemForNoPolisi.Location = New System.Drawing.Point(0, 0)
        Me.ItemForNoPolisi.Name = "ItemForNoPolisi"
        Me.ItemForNoPolisi.Size = New System.Drawing.Size(503, 26)
        Me.ItemForNoPolisi.Text = "No Polisi"
        Me.ItemForNoPolisi.TextSize = New System.Drawing.Size(90, 16)
        '
        'ItemForTipeKendaraan_id
        '
        Me.ItemForTipeKendaraan_id.Control = Me.TipeKendaraan_idSearchLookUpEdit
        Me.ItemForTipeKendaraan_id.Location = New System.Drawing.Point(0, 26)
        Me.ItemForTipeKendaraan_id.Name = "ItemForTipeKendaraan_id"
        Me.ItemForTipeKendaraan_id.Size = New System.Drawing.Size(503, 26)
        Me.ItemForTipeKendaraan_id.Text = "Tipe Kendaraan"
        Me.ItemForTipeKendaraan_id.TextSize = New System.Drawing.Size(90, 16)
        '
        'ItemForMitra_id
        '
        Me.ItemForMitra_id.Control = Me.Mitra_idSearchLookUpEdit
        Me.ItemForMitra_id.Location = New System.Drawing.Point(0, 52)
        Me.ItemForMitra_id.Name = "ItemForMitra_id"
        Me.ItemForMitra_id.Size = New System.Drawing.Size(503, 26)
        Me.ItemForMitra_id.Text = "Mitra"
        Me.ItemForMitra_id.TextSize = New System.Drawing.Size(90, 16)
        '
        'ItemForNamaSupir
        '
        Me.ItemForNamaSupir.Control = Me.NamaSupirTextEdit
        Me.ItemForNamaSupir.Location = New System.Drawing.Point(0, 78)
        Me.ItemForNamaSupir.Name = "ItemForNamaSupir"
        Me.ItemForNamaSupir.Size = New System.Drawing.Size(503, 130)
        Me.ItemForNamaSupir.Text = "Nama Supir"
        Me.ItemForNamaSupir.TextSize = New System.Drawing.Size(90, 16)
        '
        'GridColumn1
        '
        Me.GridColumn1.Caption = "Nama Tipe"
        Me.GridColumn1.FieldName = "NamaTipe"
        Me.GridColumn1.Name = "GridColumn1"
        Me.GridColumn1.Visible = True
        Me.GridColumn1.VisibleIndex = 0
        '
        'KodeMitraCol
        '
        Me.KodeMitraCol.Caption = "Kode Mitra"
        Me.KodeMitraCol.FieldName = "KodeMitra"
        Me.KodeMitraCol.Name = "KodeMitraCol"
        Me.KodeMitraCol.Visible = True
        Me.KodeMitraCol.VisibleIndex = 0
        '
        'NamaMitraCol
        '
        Me.NamaMitraCol.Caption = "Nama Mitra"
        Me.NamaMitraCol.FieldName = "NamaMitra"
        Me.NamaMitraCol.Name = "NamaMitraCol"
        Me.NamaMitraCol.Visible = True
        Me.NamaMitraCol.VisibleIndex = 1
        '
        'frmKendaraan_edit
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 16.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(867, 505)
        Me.Controls.Add(Me.DataLayoutControl1)
        Me.Name = "frmKendaraan_edit"
        Me.Text = "frmKendaraan_edit"
        Me.Controls.SetChildIndex(Me.lblHeader, 0)
        Me.Controls.SetChildIndex(Me.btnSave, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.DataLayoutControl1, 0)
        CType(Me.DataLayoutControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.DataLayoutControl1.ResumeLayout(False)
        CType(Me.NoPolisiTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TmkendaraanBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TipeKendaraan_idSearchLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.SearchLookUpEdit1View, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Mitra_idSearchLookUpEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridView1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.NamaSupirTextEdit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Root, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LayoutControlGroup1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNoPolisi, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForTipeKendaraan_id, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForMitra_id, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ItemForNamaSupir, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DxValidationProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents DataLayoutControl1 As DevExpress.XtraDataLayout.DataLayoutControl
    Friend WithEvents NoPolisiTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TmkendaraanBindingSource As BindingSource
    Friend WithEvents TipeKendaraan_idSearchLookUpEdit As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents SearchLookUpEdit1View As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents Mitra_idSearchLookUpEdit As DevExpress.XtraEditors.SearchLookUpEdit
    Friend WithEvents GridView1 As DevExpress.XtraGrid.Views.Grid.GridView
    Friend WithEvents NamaSupirTextEdit As DevExpress.XtraEditors.TextEdit
    Friend WithEvents Root As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents LayoutControlGroup1 As DevExpress.XtraLayout.LayoutControlGroup
    Friend WithEvents ItemForNoPolisi As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForTipeKendaraan_id As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForMitra_id As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents ItemForNamaSupir As DevExpress.XtraLayout.LayoutControlItem
    Friend WithEvents DxValidationProvider1 As DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider
    Friend WithEvents GridColumn1 As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents KodeMitraCol As DevExpress.XtraGrid.Columns.GridColumn
    Friend WithEvents NamaMitraCol As DevExpress.XtraGrid.Columns.GridColumn
End Class
