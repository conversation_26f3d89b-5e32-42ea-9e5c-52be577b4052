# Database Setup Guide untuk Role-Based Menu Access Control

## 🚨 **Masalah: Invalid column name 'Created<PERSON>y'**

### **Root Cause**
Error ini terjadi karena script SQL mencoba menggunakan kolom `CreatedBy`, `ModifiedBy`, dan `CreatedDate` yang mungkin belum ada di tabel database fisik, meskipun sudah didefinisikan di Entity Framework model.

### **Solusi yang Tersedia**

## 📋 **Option 1: Auto-Fix Script (Recommended)**

Gunakan script yang otomatis menambahkan kolom yang hilang:

### **File: InsertMenuData.sql**
Script ini akan:
1. <PERSON>k apakah kolom `CreatedBy`, `ModifiedBy`, `CreatedDate` ada
2. Tambahkan kolom jika belum ada
3. Insert data menu dengan kolom lengkap

```sql
-- Jalankan script ini
-- File: DxJBT2/Database/InsertMenuData.sql
```

### **File: CreateTestUsers.sql**
Script ini akan:
1. Cek dan tambahkan kolom yang diperlukan untuk tm_role dan tm_user
2. Insert test users dengan role yang berbeda

```sql
-- Jalankan script ini setelah InsertMenuData.sql
-- File: DxJBT2/Database/CreateTestUsers.sql
```

## 📋 **Option 2: Simple Script (Alternative)**

Jika Anda tidak ingin menambah kolom baru, gunakan versi sederhana:

### **File: InsertMenuData_Simple.sql**
Script ini:
1. Tidak menggunakan kolom `CreatedBy`, `ModifiedBy`
2. Hanya menggunakan kolom yang minimal diperlukan
3. Tetap berfungsi untuk testing role access

### **File: CreateTestUsers_Simple.sql**
Script ini:
1. Tidak menggunakan kolom `CreatedDate`, `CreatedBy`
2. Menggunakan kolom minimal untuk tm_role dan tm_user

## 🔧 **Manual Database Setup**

### **Step 1: Cek Struktur Tabel**
```sql
-- Cek kolom yang ada di tm_menu
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tm_menu'
ORDER BY ORDINAL_POSITION;

-- Cek kolom yang ada di tm_role_menu_access
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tm_role_menu_access'
ORDER BY ORDINAL_POSITION;

-- Cek kolom yang ada di tm_role
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tm_role'
ORDER BY ORDINAL_POSITION;

-- Cek kolom yang ada di tm_user
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tm_user'
ORDER BY ORDINAL_POSITION;
```

### **Step 2: Tambahkan Kolom yang Hilang (Jika Diperlukan)**
```sql
-- Untuk tm_menu
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_menu') AND name = 'CreatedBy')
    ALTER TABLE tm_menu ADD CreatedBy NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_menu') AND name = 'ModifiedBy')
    ALTER TABLE tm_menu ADD ModifiedBy NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_menu') AND name = 'CreatedDate')
    ALTER TABLE tm_menu ADD CreatedDate DATETIME NOT NULL DEFAULT GETDATE();

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_menu') AND name = 'ModifiedDate')
    ALTER TABLE tm_menu ADD ModifiedDate DATETIME NULL;

-- Untuk tm_role_menu_access
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_role_menu_access') AND name = 'CreatedBy')
    ALTER TABLE tm_role_menu_access ADD CreatedBy NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_role_menu_access') AND name = 'ModifiedBy')
    ALTER TABLE tm_role_menu_access ADD ModifiedBy NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_role_menu_access') AND name = 'CreatedDate')
    ALTER TABLE tm_role_menu_access ADD CreatedDate DATETIME NOT NULL DEFAULT GETDATE();

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_role_menu_access') AND name = 'ModifiedDate')
    ALTER TABLE tm_role_menu_access ADD ModifiedDate DATETIME NULL;

-- Untuk tm_role
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_role') AND name = 'CreatedBy')
    ALTER TABLE tm_role ADD CreatedBy NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_role') AND name = 'CreatedDate')
    ALTER TABLE tm_role ADD CreatedDate DATETIME NOT NULL DEFAULT GETDATE();

-- Untuk tm_user
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_user') AND name = 'CreatedBy')
    ALTER TABLE tm_user ADD CreatedBy NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('tm_user') AND name = 'CreatedDate')
    ALTER TABLE tm_user ADD CreatedDate DATETIME NOT NULL DEFAULT GETDATE();
```

## 📝 **Execution Order**

### **Recommended Sequence:**

1. **Cek struktur tabel** (Step 1 di atas)
2. **Pilih salah satu approach:**

   **Approach A (Auto-Fix):**
   ```sql
   -- 1. Jalankan InsertMenuData.sql (akan auto-add kolom)
   -- 2. Jalankan CreateTestUsers.sql (akan auto-add kolom)
   ```

   **Approach B (Simple):**
   ```sql
   -- 1. Jalankan InsertMenuData_Simple.sql
   -- 2. Jalankan CreateTestUsers_Simple.sql
   ```

   **Approach C (Manual):**
   ```sql
   -- 1. Jalankan manual ALTER TABLE commands
   -- 2. Jalankan InsertMenuData.sql
   -- 3. Jalankan CreateTestUsers.sql
   ```

## ✅ **Verification**

Setelah menjalankan script, verifikasi dengan query berikut:

```sql
-- Cek data menu
SELECT COUNT(*) as MenuCount FROM tm_menu WHERE IsActive = 1;

-- Cek data role
SELECT * FROM tm_role WHERE IsActive = 1;

-- Cek data user
SELECT u.Username, r.NamaRole 
FROM tm_user u 
LEFT JOIN tm_role r ON u.Role_id = r.Id;

-- Cek role menu access
SELECT COUNT(*) as AccessCount FROM tm_role_menu_access;

-- Test view
SELECT * FROM vw_role_menu_access WHERE RoleId = 2; -- Operator
```

## 🎯 **Expected Results**

Setelah setup berhasil:
- **tm_menu**: 18 records (5 parent + 13 child menus)
- **tm_role**: 3 records (Administrator, Operator, Viewer)
- **tm_user**: 4 records (admin, operator, viewer, norole)
- **tm_role_menu_access**: ~20 records (access permissions)

## 🚨 **Troubleshooting**

### **Error: "Cannot insert explicit value for identity column"**
**Solution**: Gunakan `SET IDENTITY_INSERT table_name ON/OFF`

### **Error: "Foreign key constraint"**
**Solution**: Pastikan tm_role sudah ada sebelum insert tm_user

### **Error: "Invalid object name 'vw_role_menu_access'"**
**Solution**: Buat view terlebih dahulu atau gunakan query langsung

### **Error: "Violation of UNIQUE KEY constraint"**
**Solution**: Hapus data existing atau gunakan `IF NOT EXISTS` check

## 📋 **Quick Fix Commands**

Jika masih ada error, jalankan command berikut untuk reset:

```sql
-- Reset data (HATI-HATI: akan hapus semua data)
DELETE FROM tm_role_menu_access;
DELETE FROM tm_user;
DELETE FROM tm_menu;
DELETE FROM tm_role;

-- Kemudian jalankan ulang script setup
```
