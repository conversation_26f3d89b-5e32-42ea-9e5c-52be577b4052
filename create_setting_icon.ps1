# Script untuk membuat ikon setting dengan simbol roda gigi
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

# Fungsi untuk membuat gambar roda gigi sebagai simbol setting
function New-SettingIcon {
    param (
        [string]$outputPath,
        [System.Drawing.Color]$backgroundColor,
        [System.Drawing.Color]$gearColor
    )
    
    try {
        # Buat bitmap 32x32 pixels dengan resolusi tinggi
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Aktifkan anti-aliasing untuk gambar yang lebih halus
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        
        # Isi dengan warna latar belakang
        $graphics.Clear($backgroundColor)
        
        # Buat brush untuk warna roda gigi
        $gearBrush = New-Object System.Drawing.SolidBrush($gearColor)
        
        # Gambar lingkaran tengah roda gigi
        $centerX = 16
        $centerY = 16
        $innerRadius = 6
        $outerRadius = 12
        $teethLength = 4
        
        # Lingkaran tengah
        $innerCircleRect = New-Object System.Drawing.Rectangle(
            ($centerX - $innerRadius),
            ($centerY - $innerRadius),
            ($innerRadius * 2),
            ($innerRadius * 2)
        )
        $graphics.FillEllipse($gearBrush, $innerCircleRect)
        
        # Gambar gigi-gigi roda
        $numTeeth = 8
        $pen = New-Object System.Drawing.Pen($gearColor, 2)
        
        for ($i = 0; $i -lt $numTeeth; $i++) {
            $angle = $i * (360 / $numTeeth)
            $radians = $angle * [Math]::PI / 180
            
            # Titik awal gigi (dari lingkaran luar)
            $startX = $centerX + $outerRadius * [Math]::Cos($radians)
            $startY = $centerY + $outerRadius * [Math]::Sin($radians)
            
            # Titik akhir gigi (ujung luar)
            $endX = $centerX + ($outerRadius + $teethLength) * [Math]::Cos($radians)
            $endY = $centerY + ($outerRadius + $teethLength) * [Math]::Sin($radians)
            
            # Gambar garis untuk gigi
            $graphics.DrawLine($pen, $startX, $startY, $endX, $endY)
        }
        
        # Gambar lingkaran luar yang menghubungkan gigi-gigi
        $outerCircleRect = New-Object System.Drawing.Rectangle(
            ($centerX - $outerRadius),
            ($centerY - $outerRadius),
            ($outerRadius * 2),
            ($outerRadius * 2)
        )
        $graphics.DrawEllipse($pen, $outerCircleRect)
        
        # Simpan gambar
        $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Bersihkan resources
        $pen.Dispose()
        $gearBrush.Dispose()
        $graphics.Dispose()
        $bitmap.Dispose()
        
        Write-Host "Gambar setting berhasil dibuat: $outputPath"
    }
    catch {
        Write-Host "Error saat membuat gambar setting: $_"
    }
}

# Buat direktori Resources jika belum ada
$resourcesDir = "d:\SourceCode\Bright\DX24_1\DxJBT2\DxJBT2\Resources"
if (-not (Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir
}

# Buat ikon untuk menu Setting
$settingPath = Join-Path $resourcesDir "Setting_32x32.png"
New-SettingIcon -outputPath $settingPath -backgroundColor ([System.Drawing.Color]::FromArgb(52, 73, 94)) -gearColor ([System.Drawing.Color]::White)

Write-Host "Pembuatan ikon setting selesai."
