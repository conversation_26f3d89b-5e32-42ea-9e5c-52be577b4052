# Script untuk membuat ikon menu transaksi dan jembatan timbang
Add-Type -AssemblyName System.Drawing

# Fungsi untuk membuat gambar sederhana dengan teks
function Create-IconWithText {
    param (
        [string]$text,
        [string]$outputPath,
        [System.Drawing.Color]$backgroundColor,
        [System.Drawing.Color]$textColor
    )
    
    try {
        # Buat bitmap 32x32 pixels
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Isi dengan warna latar belakang
        $graphics.Clear($backgroundColor)
        
        # Siapkan font dan brush untuk teks
        $fontFamily = New-Object System.Drawing.FontFamily('Arial')
        $fontSize = 7
        $fontStyle = [System.Drawing.FontStyle]::Bold
        $font = New-Object System.Drawing.Font($fontFamily, $fontSize, $fontStyle)
        $brush = New-Object System.Drawing.SolidBrush($textColor)
        
        # Hitung posisi teks agar berada di tengah
        $textSize = $graphics.MeasureString($text, $font)
        $x = (32 - $textSize.Width) / 2
        $y = (32 - $textSize.Height) / 2
        
        # Gambar teks
        $graphics.DrawString($text, $font, $brush, $x, $y)
        
        # Simpan gambar
        $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Bersihkan resources
        $graphics.Dispose()
        $bitmap.Dispose()
        
        Write-Host "Gambar berhasil dibuat: $outputPath"
    }
    catch {
        Write-Host "Error saat membuat gambar: $_"
    }
}

# Buat direktori Resources jika belum ada
$resourcesDir = "d:\SourceCode\Bright\DX24_1\DxJBT2\DxJBT2\Resources"
if (-not (Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir
}

# Buat ikon untuk menu Transaksi
$transaksiPath = Join-Path $resourcesDir "Transaksi_32x32.png"
Create-IconWithText -text "TR" -outputPath $transaksiPath -backgroundColor ([System.Drawing.Color]::FromArgb(0, 120, 215)) -textColor ([System.Drawing.Color]::White)

# Buat ikon untuk menu Jembatan Timbang
$jembatanPath = Join-Path $resourcesDir "JembatanTimbang_32x32.png"
Create-IconWithText -text "JT" -outputPath $jembatanPath -backgroundColor ([System.Drawing.Color]::FromArgb(0, 150, 136)) -textColor ([System.Drawing.Color]::White)

# Buat ikon untuk menu Tipe Kendaraan
$tipeKendaraanPath = Join-Path $resourcesDir "TipeKendaraan_32x32.png"
Create-IconWithText -text "TK" -outputPath $tipeKendaraanPath -backgroundColor ([System.Drawing.Color]::FromArgb(230, 126, 34)) -textColor ([System.Drawing.Color]::White)

# Buat ikon untuk menu Kendaraan
$kendaraanPath = Join-Path $resourcesDir "Kendaraan_32x32.png"
Create-IconWithText -text "KD" -outputPath $kendaraanPath -backgroundColor ([System.Drawing.Color]::FromArgb(155, 89, 182)) -textColor ([System.Drawing.Color]::White)

Write-Host "Pembuatan ikon selesai."
