-- Tabel Master <PERSON><PERSON><PERSON>
-- Menyimpan data master jenis kemasan dengan ukuran dan berat per unit

CREATE TABLE tm_kemasan (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    KodeKemasan NVARCHAR(20) NOT NULL UNIQUE,
    NamaKemasan NVARCHAR(100) NOT NULL,
    UkuranKemasan NVARCHAR(50) NULL, -- Contoh: "200L", "50kg", "25kg", dll
    Berat<PERSON>emasan DECIMAL(10,3) NOT NULL, -- Berat kemasan kosong dalam kg
    Satuan NVARCHAR(20) NOT NULL DEFAULT 'pcs', -- <PERSON><PERSON><PERSON> kemasan (pcs, unit, dll)
    Keterangan NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50) NULL,
    ModifiedDate DATETIME NULL,
    ModifiedBy NVARCHAR(50) NULL
);

-- Index untuk performa
CREATE INDEX IX_tm_kema<PERSON>_KodeKemasan ON tm_kemasan(KodeKemasan);
CREATE INDEX IX_tm_kemasan_IsActive ON tm_kemasan(IsActive);

-- Insert sample data
INSERT INTO tm_kemasan (KodeKemasan, NamaKemasan, UkuranKemasan, BeratKemasan, Satuan, Keterangan) VALUES
('DRUM200', 'Drum Plastik', '200 Liter', 15.500, 'pcs', 'Drum plastik kapasitas 200 liter'),
('DRUM100', 'Drum Plastik', '100 Liter', 8.750, 'pcs', 'Drum plastik kapasitas 100 liter'),
('KARUNG50', 'Karung Plastik', '50 kg', 0.250, 'pcs', 'Karung plastik kapasitas 50 kg'),
('KARUNG25', 'Karung Plastik', '25 kg', 0.150, 'pcs', 'Karung plastik kapasitas 25 kg'),
('JERIGEN30', 'Jerigen Plastik', '30 Liter', 2.100, 'pcs', 'Jerigen plastik kapasitas 30 liter'),
('JERIGEN20', 'Jerigen Plastik', '20 Liter', 1.500, 'pcs', 'Jerigen plastik kapasitas 20 liter'),
('SACK_PP', 'Sack Polypropylene', '50 kg', 0.180, 'pcs', 'Sack PP untuk bahan curah'),
('BIGBAG', 'Big Bag/FIBC', '1000 kg', 3.500, 'pcs', 'Flexible Intermediate Bulk Container'),
('PALLET_KAYU', 'Pallet Kayu', 'Standard', 25.000, 'pcs', 'Pallet kayu standard'),
('PALLET_PLASTIK', 'Pallet Plastik', 'Standard', 8.500, 'pcs', 'Pallet plastik standard');
